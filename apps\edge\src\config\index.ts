import { checkFileExistsSync, createFile, getFile, getResourcesPath, getSyncFile } from '@/utils'
import path from 'path'
import { app } from 'electron'
import fs from 'fs'
// import { getInstallPath } from '@/main/AINowService'

export const getMicroPath = () => path.join(app.getPath('userData'), 'AINowPlugins')
export const getToolPath = () =>
  getResourcesPath(path.join(__dirname, '../..', import.meta.env.MAIN_VITE_TOOLS_PATH))

const domain = 'http://teamai-test.lenovo.com'

class ConfigClass {
  private initPromise: Promise<void> | null = null
  constructor() {}

  init(): Promise<void> {
    if (!this.initPromise) {
      this.initPromise = this.initConfig()
    }
    return this.initPromise
  }

  private async initConfig(): Promise<void> {
    const configExist = checkFileExistsSync(this.configPath)
    let existingConfig = {}

    if (configExist) {
      try {
        const file = await getFile(this.configPath)
        existingConfig = JSON.parse(file || '{}')
      } catch (e) {
        existingConfig = {}
      }
    }
    const newConfig = { ...existingConfig, ...this.defaultConfig }
    this.curConfig = newConfig

    try {
      if (fs.existsSync(this.configPath)) {
        fs.unlinkSync(this.configPath)
      }
      await createFile(this.configPath, JSON.stringify(newConfig, null, 2))
    } catch (e) {
      this.curConfig = this.defaultConfig
    }
  }

  defaultConfig = {
    // authServer: `${domain}:9080/auth`,
    // edgeServer: `${domain}:9070/ainow-user-acl-api`,
    // kbFileServer: `${domain}:9070/lr-kb-service-api`,
    // modelServer: `${domain}:8099/api/v1/flow/c7667aa7-984a-4b49-a3b6-295e82dd705a`,
    // historyServer: `${domain}:8102/api/v1`,
    // chunkServer: `${domain}:8099/api/v1`
    authServer: 'http://10.183.158.11:9080/auth',
    edgeServer: 'http://10.183.158.11:9070/ainow-user-acl-api',
    kbFileServer: 'http://10.183.158.11:9070/lr-kb-service-api',
    modelServer: 'http://10.176.238.83:8099/api/v1/flow/c7667aa7-984a-4b49-a3b6-295e82dd705a',
    historyServer: 'http://10.176.238.83:8102/api/v1',
    chunkServer: 'http://10.176.238.83:8099/api/v1'
  }
  curConfig: Record<string, string | boolean | number> = {}
  configPath = path.join(app.getPath('userData'), 'config.json')
  async getConfig(needUpDate = false) {
    await this.init()
    if (needUpDate) {
      await this.updateConfig()
    }
    return this.curConfig
  }
  async updateConfig() {
    const file = getSyncFile(this.configPath)
    this.curConfig = JSON.parse(file)
  }
}
export const Config = new ConfigClass()
