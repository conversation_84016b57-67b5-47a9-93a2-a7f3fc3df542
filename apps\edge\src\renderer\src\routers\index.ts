// import { config, userInfo } from '@/hooks/const'
import {
  createRouter,
  createWebHashHistory,
  type RouteRecordRaw
  // RouterView,
} from 'vue-router'
// 修正后的路由配置
export const routes: RouteRecordRaw[] = [
  {
    name: 'main',
    path: '/main',
    component: async () => await import('@renderer/views/Main'),
    redirect: '/main/chat',
    children: [
      {
        name: 'chat',
        path: 'chat',
        component: async () => await import('@renderer/views/Chat')
      },
      {
        name: 'knowledgeBase',
        path: 'knowledgeBase',
        component: async () => await import('@renderer/views/KnowledgeBase')
      },
      {
        name: 'explore',
        path: 'explore',
        component: async () => await import('@renderer/views/Explore')
      },
      {
        name: 'agent',
        path: 'explore/agent',
        component: async () => await import('@renderer/views/Agent')
      },
      {
        name: 'standardKnowledgeBase',
        path: 'standardKnowledgeBase',
        component: () => import('@renderer/views/StandardKnowledgeBase')
      },
      {
        name: 'userManagement',
        path: 'userManagement',
        component: () => import('@renderer/views/UserManagement')
      },
      {
        name: 'myKnowledge',
        path: 'myKnowledge',
        component: () => import('@renderer/views/MyKnowledge')
      },
      {
        name: 'micTest',
        path: 'micTest',
        component: () => import('@renderer/views/MicTest')
      }
    ]
  },
  {
    path: '/',
    redirect: '/main'
  }
]

const router = createRouter({
  history: createWebHashHistory(),
  routes
})

router.beforeEach((to, from, next) => {
  next()
})
export default router
