<template>
  <div class="ainow-layout">
    <AConfigProvider
      :theme="{
        token: {
          colorPrimary: themeVars['--primary-color'],
          fontFamily: themeVars['--font-family'],
          borderRadius: `${2}px`
        }
      }"
    >
      <div :class="{ 'ainow-layout_menu': true, 'ainow-layout_menu-collapsed': collapsed }">
        <div class="ainow-layout_menu_header">
          <!-- <div class="ainow-layout_menu_title" ref="title" v-show="!collapsed">
            {{ appName }}
          </div> -->
          <div class="ainow-layout_menu_collapse" @click="handleCollapse">
            <SvgIcon name="Collapse" size="22" :class="{ 'collapse-icon-rotated': collapsed }" />
          </div>
        </div>
        <div class="ainow-layout_menu_top-slot" @click="setAddSession">
          <slot name="top-slot" :collapsed="collapsed"></slot>
        </div>
        <ABtn
          :class="{
            'ainow-layout_menu_item': true,
            'ainow-layout_menu_item-selected': isSelected(item)
          }"
          size="large"
          v-for="item in menuItems"
          block
          type="text"
          @click="handleRouter(item)"
        >
          <div class="ainow-layout_menu_item_content">
            <!-- <component :is="item.icon"> </component> -->
            <SvgIcon
              class="ainow-layout_menu_item_content_icon menu-icon"
              :name="`${item.name}-icon`"
              size="48"
            ></SvgIcon>
            <span class="ainow-layout_menu_item_content_title" v-show="!collapsed">
              {{ item.name }}
            </span>
          </div>
        </ABtn>
        <div class="ainow-layout_menu_bottom">
          <div class="ainow-layout__admin-button-wrapper" v-if="!shouldHideMyKnowledge">
            <ABtn
              block
              class="ainow-layout__admin-button"
              :class="{
                'ainow-layout__admin-button_active': ['myKnowledge', 'knowledgeBase'].includes(
                  String(route.name)
                )
              }"
              type="text"
              @click="handleRouter('myKnowledge')"
            >
              <div class="ainow-layout_menu_item_content">
                <SvgIcon size="22" class="ainow-layout__admin-button-icon" name="MKB"></SvgIcon>
                <span class="ainow-layout__admin-button-text" v-show="!collapsed"
                  >My Knowledge</span
                >
              </div>
            </ABtn>
            <!-- <ABtn
              block
              class="ainow-layout__admin-button"
              :class="{
                'ainow-layout__admin-button_active': route.name === 'explore'
              }"
              type="text"
            >
              <div class="ainow-layout_menu_item_content">
                <SvgIcon size="20" class="ainow-layout__admin-button-icon" name="Explore"></SvgIcon>
                <span class="ainow-layout__admin-button-text" v-show="!collapsed"
                  >Agent Management</span
                >
              </div>
            </ABtn> -->
            <ABtn
              v-if="['Super Administrator', 'Administrator'].includes(GlobalConfig.userInfo.role)"
              block
              class="ainow-layout__admin-button"
              :class="{
                'ainow-layout__admin-button_active': route.name === 'userManagement'
              }"
              type="text"
              @click="handleRouter('micTest')"
            >
              <div class="ainow-layout_menu_item_content">
                <SvgIcon
                  size="20"
                  class="ainow-layout__admin-button-icon"
                  name="user-management-icon"
                ></SvgIcon>
                <span class="ainow-layout__admin-button-text" v-show="!collapsed"
                  >User Management</span
                >
              </div>
            </ABtn>
          </div>
          <a-popover placement="rightBottom" :arrow="false" overlayClassName="layout-menu-popover">
            <template #content>
              <div class="ainow-layout_menu_bottom_popover">
                <SvgIcon
                  size="28"
                  class="ainow-layout_menu_bottom_item_icon"
                  name="Avatar_out"
                ></SvgIcon>
                <span class="ellipsis">{{ GlobalConfig.tokens.lenovo_username }}</span>
              </div>
              <ABtn block type="text" @click="toSignout">Sign out</ABtn>
            </template>
            <ABtn size="large" block class="ainow-layout_menu_bottom_item" type="text">
              <SvgIcon size="28" class="ainow-layout_menu_bottom_item_icon" name="Avatar"></SvgIcon>
              <span class="ainow-layout_menu_bottom_item_title" v-show="!collapsed">Account</span>
            </ABtn>
          </a-popover>
        </div>
      </div>
      <div class="ainow-layout_main">
        <HistoryList
          v-if="!historyCollapsed && route.name === 'chat'"
          :collapsed="collapsed"
          :selectedItemId="selectedHistoryItemId"
          @newChat="handleNewChat"
          @toggleHistory="handleHistoryCollapse"
          @item-selected="selectedHistoryItemId = $event"
        />
        <div class="ainow-layout_view" ref="chatDiv">
          <div class="chat-header-icons" v-if="historyCollapsed && route.name === 'chat'">
            <div class="collapsed-icon-container">
              <SvgIcon
                class="history-expand-icon"
                name="historyExpand"
                size="26"
                @click="handleHistoryCollapse"
              />
            </div>
            <AddBtn :collapsed="false" @click="handleNewChat" />
          </div>
          <div
            class="ainow-layout_view_header"
            :class="{
              'ainow-layout__view-header_white': [
                'knowledgeBase',
                'userManagement',
                'myKnowledge'
              ].includes(route.name as string)
            }"
          >
            <div class="ainow-layout_view_header_dragger" ref="dragger"></div>
          </div>
          <div class="ainow-layout_view_content">
            <router-view v-slot="{ Component }">
              <keep-alive>
                <component
                  :sessionNum="sessionNum"
                  :is="Component"
                  :isComponentSmall="
                    (isRefExpanded || isCitationsExpanded) && !historyCollapsed && !collapsed
                  "
                ></component>
              </keep-alive>
            </router-view>
          </div>
        </div>
        <div v-show="isRefExpanded && route.name === 'chat'" class="deepThinkRefWidth">
          <deepThinkRef :collapsed="collapsed" />
        </div>
        <div v-show="isCitationsExpanded && route.name === 'chat'" class="deepThinkCiticlesWidth">
          <deepThinkCiticles :collapsed="collapsed" />
        </div>
      </div>
      <AModal
        v-model:open="signoutModal"
        :mask="false"
        title="Signout"
        okText="Sign Out"
        centered
        @ok="toSignout(false, true)"
      >
        <div class="signout-modal_content">
          <SvgIcon name="Out" size="24"></SvgIcon>
          <div class="signout-modal_content_text global-text3">Are your sure to sign out？</div>
        </div>
      </AModal>
    </AConfigProvider>
  </div>
</template>

<script setup lang="ts">
// import { AMenuItem } from '../type'
import { themeHooks, AConfigProvider, APopover, AModal, ABtn, Result } from '@libs/a-comps'
import { electronHooks } from '../../electron'
import { computed, defineProps, onMounted, ref, VNode, provide } from 'vue'
import { useRoute, useRouter, RouteLocationRaw } from 'vue-router'
import { getUserInfo } from '@/services'
import SvgIcon from '../SvgIcon/SvgIcon.vue'
import HistoryList from '../HistoryList/HistoryList.vue'
import deepThinkRef from '../RefComponent/deepThinkRef.vue'
import deepThinkCiticles from '../RefComponent/deepThinkCiticles.vue'
import AddBtn from '../../views/Main/comps/AddBtn.vue'
import { dragHooks } from '../../hooks/Drag'
import { handleSignout } from '../../hooks/Auth'
import { GlobalConfig } from '../../common'
import mySDK from '@renderer/views/Chat/sdkService'
import { emitter } from '@/utils/EventBus'
import { ReferencesType } from '@libs/a-comps/ChatBaseComponent/types'

const emit = defineEmits(['rel-Collapsed', 'Collapsed'])
const chatDiv = ref()
const isRefExpanded = ref(false)
const isCitationsExpanded = ref(false)
emitter.on('ref-click-2', (result: boolean) => {
  isRefExpanded.value = result
  // 对citicle-panel 进行关闭
  isCitationsExpanded.value = false
})

emitter.on('ref-click-expand', (result: boolean) => {
  isCitationsExpanded.value = result
  if (result && !collapsed.value) {
    collapsed.value = !collapsed.value
  }
  // 对ref-panel 进行关闭
  isRefExpanded.value = false
})

emitter.on('ref-click-citicleClose', (result: boolean) => {
  isCitationsExpanded.value = result
})

import { emitAgentChanged } from '../../../../utils'

// import { WinState } from '@/types'
export interface MenuItem {
  name: string
  icon: VNode
  to: RouteLocationRaw
  resourceType?: string
}

withDefaults(
  defineProps<{
    appName?: string
    menuItems: MenuItem[]
    shouldHideMyKnowledge?: boolean
  }>(),
  {
    appName: 'Lenovo',
    shouldHideMyKnowledge: false
  }
)
const route = useRoute()
const router = useRouter()
const title = ref()
const dragger = ref()
const collapsed = ref(false)
const historyCollapsed = ref(true)
const selectedHistoryItemId = ref<string | null>(null)
const signoutModal = ref(false)
const fullPath = computed(() => route.fullPath)
const { getCurrentVars } = themeHooks()
const { setDrag } = dragHooks()
const sessionNum = ref(0)
const divwidth = ref()
const setAddSession = () => {
  sessionNum.value++
}

const themeVars = getCurrentVars()
const handleCollapse = () => {
  collapsed.value = !collapsed.value
}
const handleHistoryCollapse = () => {
  historyCollapsed.value = !historyCollapsed.value
  emitter.emit(
    'getcurrent-width',
    (chatDiv.value as HTMLElement).offsetWidth,
    historyCollapsed.value
  )
}
const isSelected = (item: MenuItem): boolean => {
  const { to } = item
  if (typeof to === 'string') {
    return fullPath.value.toLowerCase().includes(to.toLowerCase())
  }
  if (typeof to === 'object' && to !== null && 'name' in to && to.name) {
    if (route.name !== to.name) {
      return false
    }

    if (to.name === 'chat' && item.name === 'Knowledge Agent') {
      const knowledgeAgentParams = ['id', 'origin', 'name', 'agentId']
      return knowledgeAgentParams.some((param) => route.query[param] !== undefined)
    }

    if (to.name === 'agent' && item.name !== 'Knowledge Agent') {
      const target = (to as any).query || {}
      // 严格匹配唯一标识，避免多个 Agent（如 Email/Video）同时被选中
      if (target.agentId !== undefined)
        return String(route.query.agentId || '') === String(target.agentId)
      if (target.agentUrl !== undefined)
        return String(route.query.agentUrl || '') === String(target.agentUrl)
      if (target.id !== undefined) return String(route.query.id || '') === String(target.id)
      // 未匹配到标识时，继续走下方对 query 的逐项比对
    }

    if (to.query) {
      for (const key in to.query) {
        if (route.query[key] !== to.query[key]) {
          return false
        }
      }
    }
    return true
  }
  return false
}
const toSignout = (open: boolean = true, signout?: boolean) => {
  console.log(open, signout)

  signoutModal.value = open
  if (signout) {
    handleSignout()
    console.log(GlobalConfig)
  }
}
const electronApi = electronHooks()

function handleRouter(item: MenuItem | RouteLocationRaw): void {
  if (typeof item === 'string' || (typeof item === 'object' && !('to' in item))) {
    router.push({ name: item as string })
    return
  }
  const { to, resourceType } = item
  if (typeof to === 'object' && to && 'query' in to && to.query && 'agentId' in to.query) {
    emitAgentChanged(to.query.agentId as string, resourceType)
  }
  router.push(to)
}

/**
 * 更新用户信息
 * @return
 */
const updateUserInfo = async () => {
  try {
    const result = await getUserInfo()
    const { data, success } = result.data

    if (success && data) {
      const { role } = data
      GlobalConfig.userInfo.role = role
    }
  } catch (error) {
    console.error('updateUserInfo error', error)
  }
}

const handleNewChat = () => {
  selectedHistoryItemId.value = null

  const currentQuery = route.query
  const knowledgeAgentParams = ['id', 'origin', 'name', 'agentId']
  const hasKnowledgeAgentContext = knowledgeAgentParams.some(
    (param) => currentQuery[param] !== undefined
  )

  if (hasKnowledgeAgentContext) {
    const newQuery: any = {}
    knowledgeAgentParams.forEach((param) => {
      if (currentQuery[param] !== undefined) {
        newQuery[param] = currentQuery[param]
      }
    })
    router.push({ name: 'chat', query: newQuery })
  } else {
    router.push({ name: 'chat' })
  }
  emitter.emit('ref-click-2', false)
  window.dispatchEvent(new CustomEvent('newChatCreated'))
  // emitter.emit('getcurrent-width', (chatDiv.value as HTMLElement).offsetWidth)
}

provide('historyCollapsed', historyCollapsed)
provide('handleHistoryCollapse', handleHistoryCollapse)

onMounted(() => {
  if (electronApi) {
    title.value && setDrag(title.value)
    dragger.value && setDrag(dragger.value)
  }

  updateUserInfo()
})
// onMounted(() => {
//   const observer = new ResizeObserver((entries) => {})
//   observer.observe(chatDiv.value)
// })
</script>

<style lang="less" scoped>
.ainow-layout {
  display: flex;
  height: 100%;
  background: linear-gradient(
    121.75deg,
    rgba(241, 242, 255, 0.96) 0.51%,
    rgba(247, 249, 255, 0.96) 40.31%,
    rgba(234, 241, 255, 0.96) 100%
  );
  outline: 1px solid rgba(0, 0, 0, 0.06);
  backdrop-filter: blur(40px);

  .deepThinkRefWidth {
    width: 376px;
  }
  .deepThinkCiticlesWidth {
    width: 592px;
  }

  .menu-icon {
    color: #52525b;
  }

  .ainow-layout_menu_item-selected .menu-icon,
  .ainow-layout_menu_item:hover .menu-icon,
  .ainow-layout_menu_bottom_item:hover .menu-icon,
  .ainow-layout_view_header_collapse:hover .menu-icon {
    color: #6441ab;
  }

  .ainow-layout_menu_item.ainow-layout_menu_item-selected {
    background-color: var(--hover-color);
    color: #6441ab;
    border-radius: 8px;
  }
  .ainow-layout_menu_item-selected .ainow-layout_menu_item_content_icon {
    color: #6441ab;
  }
  .ainow-layout_menu_item-selected .ainow-layout_menu_item_content {
    color: #6441ab;
    font-weight: 600;
  }

  .ainow-layout_menu_bottom_popover {
    display: flex;
    align-items: center;
    // gap: 1px;
    padding: 8px 0;
    border-bottom: 1px solid #0000000f;
  }

  &_history-panel_collapsed {
    width: 0;
  }

  &_main {
    display: flex;
    flex: 1;
  }

  &_menu {
    width: 210px;
    height: 100vh;
    // background: linear-gradient(
    //   121.75deg,
    //   rgba(241, 242, 255, 0.92) 100%,
    //   rgba(247, 249, 255, 0.92) 40.31%,
    //   rgba(234, 241, 255, 0.92) 0.51%
    // );
    background-color: rgba(255, 255, 255, 0.24);
    // background-image: linear-gradient(180deg, rgba(255, 255, 255, 0.8) 0%, rgba(241, 241, 255, 0.75) 100%);
    padding: 0 12px;
    flex-shrink: 0;
    box-sizing: border-box;
    transition: all 0.2s;
    position: relative;
    border-right: 1px solid rgba(0, 0, 0, 0.06);
    // box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);

    &_header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 40px;
    }

    &_collapse {
      cursor: pointer;
      padding: 6px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 4px;
      transition: all 0.2s;
      flex-shrink: 0;
      margin-top: 56px;
      color: #52525b;

      &:hover {
        background-color: var(--hover-color);
      }

      .collapse-icon-rotated {
        transform: rotate(180deg);
      }
    }

    &_top-slot {
      width: 100%;
      margin-top: 20px;
    }

    &_bottom {
      position: absolute;
      bottom: 32px;
      width: calc(100% - 24px);
      // padding-right: 12px;

      &_item {
        display: flex;
        align-items: center;
        // justify-content: start;
        padding-left: 12px;
        margin-top: 12px;
        border-radius: 8px;
        transition: all 0.2s;
        height: 48px;
        font-weight: 600;
        font-size: 13px;

        .ainow-layout_menu_bottom_item_icon {
          color: #ffffff;
        }

        &:hover {
          background-color: #f5f5f5;

          .ainow-layout_menu_bottom_item_icon {
            color: #6441ab;
          }

          .ainow-layout_menu_bottom_item_title {
            color: #6441ab;
            font-weight: 600;
            // font-size: 13px;
          }
        }

        // &:focus {
        //   background-color: var(--hover-color);

        //   .ainow-layout_menu_bottom_item_icon {
        //     color: #6441ab;
        //   }

        //   .ainow-layout_menu_bottom_item_title {
        //     color: #6441ab;
        //     font-weight: 600;
        //   }
        // }

        &_icon {
          margin-right: 12px;
          flex-shrink: 0;
          color: #52525b;
          transition: color 0.2s;
        }

        &_title {
          color: var(--text-color3);
          font-size: 13px;
          transition: all 0.2s;
        }
      }
    }

    .ainow-layout__admin-button-wrapper {
      padding-bottom: 18px;
      border-bottom: 1px solid #0000000f;
      display: flex;
      flex-direction: column;
      gap: 10px;
    }

    .ainow-layout__admin-button {
      padding: 0;
      text-align: initial;
      padding-left: 13.5px;
      justify-content: flex-start;
      height: 48px;
      display: flex;
      align-items: center;
      border-radius: 8px;
      &:hover {
        background-color: #f5f5f5;
        color: #6441ab;
        border-radius: 8px;
        font-size: 600;
        .ainow-layout__admin-button-icon {
          color: #6441ab;
        }
        .ainow-layout__admin-button-text {
          color: #6441ab;
          font-weight: 600;
        }
      }
      &:focus {
        background-color: #e8e6f6;
      }
    }

    .ainow-layout__admin-button_active {
      background-color: var(--hover-color);
      color: #6441ab;
      border-radius: 8px;

      .ainow-layout__admin-button-icon {
        color: #6441ab;
      }

      .ainow-layout__admin-button-text {
        color: #6441ab;
        font-weight: 600;
      }
    }

    .ainow-layout__admin-button-icon {
      margin-right: 16.5px;
    }

    .ainow-layout__admin-button-text {
      font-size: 13px;
      font-weight: 600;
    }

    &_item {
      height: 48px;
      cursor: pointer;
      font-weight: 600;
      font-size: 14px;
      margin-top: 20px;
      padding: 0;
      justify-content: flex-start;

      &_content {
        display: flex;
        align-items: center;
        color: #52525b;

        // > *:first-child {
        //   margin-right: 4px;
        //   flex-shrink: 0;
        // }

        .ainow-layout_menu_item_content_icon {
          margin-right: 4px;
          flex-shrink: 0;
        }

        &:hover {
          color: #6441ab;
          border-radius: 8px;
        }
      }

      &:hover {
        background-color: #f5f5f5;
        color: #6441ab;
        border-radius: 8px;

        .ainow-layout_menu_item_content {
          color: #6441ab;
          font-size: 14px;
        }
      }

      &:focus {
        background-color: var(--hover-color);
        color: #6441ab;
        border-radius: 8px;

        .ainow-layout_menu_item_content {
          color: #6441ab;
          font-size: 14px;
        }
      }
    }

    &_title {
      cursor: pointer;
      font-weight: 600;
      color: var(--text-color3);
      overflow: hidden;
      text-align: left;
      flex-grow: 1;
      // app-region: drag;
    }

    @media print {
      &_title {
        overflow: visible;
      }
    }

    &-collapsed {
      width: 74px;

      .ainow-layout_menu_header {
        justify-content: center;
      }

      .ainow-layout_menu_item_content {
        justify-content: center;
        font-size: 14px;
        font-weight: 600;

        > *:first-child {
          margin-right: 0;
        }
      }

      .ainow-layout_menu_bottom_item {
        justify-content: center;
        padding-left: 0;

        &_icon {
          margin-right: 0;
        }
      }
    }
  }

  &_view {
    flex: 1;
    height: 100%;
    background: rgba(256, 256, 256, 0.98);
    backdrop-filter: blur(10px);
    position: relative;

    &_header {
      height: 40px;
      color: var(--text-color3);
      background: linear-gradient(
        121.75deg,
        rgba(241, 242, 255, 0.92) 0.51%,
        rgba(247, 249, 255, 0.92) 100%
      );
      display: flex;
      justify-content: space-between;

      &_dragger {
        flex: 1;
      }
    }

    &_content {
      height: calc(100% - 40px);
    }
  }

  .ainow-layout__view-header_white {
    background: #fff;
  }
}

.chat-header-icons {
  position: absolute;
  top: 16px;
  left: 15px;
  z-index: 10;
  display: flex;
  align-items: center;
  gap: 8px;

  .collapsed-icon-container {
    display: inline-flex;
    :hover {
      background-color: white;
      border-radius: 3px;
    }
  }

  .history-expand-icon {
    cursor: pointer;
  }

  :deep(.add-btn-container) {
    padding: 0;
    width: auto;
  }

  :deep(.add-btn.ant-btn-primary) {
    width: auto;
  }
}

.signout-modal_content {
  text-align: center;
  padding: 16px;

  > * {
    &:first-child {
      margin-bottom: 24px;
    }
  }
}
:deep(.layout-menu-popover.ant-popover) {
  .ant-popover-inner {
    border-radius: 8px;
  }
}
</style>
