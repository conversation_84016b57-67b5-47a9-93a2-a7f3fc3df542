<template>
  <div class="agent-view">
    <!-- <div class="agent-view_title global-title2" @click="handleTitleClick">< {{ agent.name }}</div> -->
    <div class="agent-view_content">
      <iframe
        v-if="isIframeActive && getTargetUrl(agent.origin, agent.agentUrl)"
        :src="getTargetUrl(agent.origin, agent.agentUrl)"
        frameborder="0"
        width="100%"
        height="100%"
        allow="microphone *"
      ></iframe>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onActivated, onDeactivated } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { GlobalConfig } from '../../common'
const router = useRouter()

const route = useRoute()
const agent = computed(() => {
  return route.query as { id: string; origin: string; name: string; agentUrl: string }
})

const isIframeActive = ref(true)

onActivated(() => {
  isIframeActive.value = true
})

onDeactivated(() => {
  isIframeActive.value = false
})

const getTargetUrl = (origin: string, agetnUrl: string) => {
  if (origin === 'email') {
    // return (
    //   GlobalConfig.emailUrl +
    //   `?client_id=${GlobalConfig.tokens.lenovo_id}&access_token=${GlobalConfig.tokens.access_token}`
    // )
    return `${agetnUrl}?client_id=${GlobalConfig.tokens.lenovo_id}&access_token=${GlobalConfig.tokens.access_token}`
  }
  if (origin === 'xinliu') {
    // return GlobalConfig.videoUrl + `?client_id=${GlobalConfig.tokens.lenovo_id}`
    return agetnUrl
  }
  return 'http://www.baidu.com'
}
const handleTitleClick = () => {
  router.go(-1)
}
</script>

<style lang="less" scoped>
.agent-view {
  // padding: 8px;
  padding: 0 8px 8px 0;
  height: 100%;

  &_title {
    margin-bottom: 16px;
    cursor: pointer;
  }

  &_content {
    height: 100%;
  }
}
</style>
