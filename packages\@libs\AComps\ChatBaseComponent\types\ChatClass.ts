import { nextTick, reactive, ref } from "vue";
import MarkdownIt from 'markdown-it';
import { type WelcomeType, type QuestionType, type AnswerType, type StreamAnswerType, ChatStatus, ChatComponentType, ChatMessageType, ChatSDK, historyItemType, ReferencesType, DocumentListType, CitationsType, ToolType } from "../types";
import { ChatEventBus, sendServiceMsg, stopServiceMsg, setChatSDK } from "./AIServiceGateway";
import { v4 as uuidv4 } from 'uuid';


// Question 类，处理与问题相关的数据
export class Question {
    questionData: QuestionType;
    id: string = ""

    constructor() {
        this.questionData = {} as QuestionType; // 初始化为空对象
    }

    setData(data: QuestionType): void {
        this.questionData = data;
        this.id = data.id
    }

    getData(): QuestionType {
        return this.questionData;
    }
}

// Answer 类，处理与回答相关的数据
export class Answer {
    answerData: AnswerType;
    id: string = ""
    markdownParser: MarkdownIt;
    chatStatusAnswer = reactive<{ value: ChatStatus }>({ value: ChatStatus.DONE });; // 当前回答状态默认结束状态
    refreshArr: Answer[] = [];
    hasAddedToRefresh: boolean = false; // 标记是否已添加到refreshArr
    isFromHistory: boolean = false; // 标记是否从历史会话加载
    originalContent?: string; // 保存原始内容（用于翻页时恢复）
    originalResponse?: string; // 保存原始response
    originalReferences?: ReferencesType[]; // 保存原始references（用于翻页时恢复）
    originalCitations?: any; // 保存原始citations（用于翻页时恢复）
    originalThink?: any; // 保存原始citations（用于翻页时恢复）
    originalThinkingContent?: string; // 保存原始thinkingContent（用于翻页时恢复）
    tempContent?: string; // 临时保存当前内容（用于切换历史页码数据）
    tempResponse?: string; // 临时保存当前response
    tempThinkingContent?: string; // 临时保存当前thinkingContent
    references = reactive<ReferencesType[]>([])
    // citations = reactive<CitationsType[]>([])
    citations: any;
    think: any;
    documentList: DocumentListType[] = []
    fileSearchList: DocumentListType[] = [] // file search 选择的文档
    isAgent: string = ''; // 是否是agent回答
    isExpanded: boolean = true
    isDeepThink: boolean = false
    originalIsDeepThink?: boolean // 历史记录原始的深度思考标记
    regen:boolean = false
    thinkingContent:string = ""
    stepMode:string = ""
    toolIndex:string = ""
    toolType = reactive<ToolType[]>([])
    olds?:any[]
    constructor() {
        this.references = []
        this.toolType = []
        this.citations = {}
        this.think = []
        this.answerData = {
            response: "",
            content: ""
        } as AnswerType; // 初始化为空对象
        this.markdownParser = new MarkdownIt();
        this.hasAddedToRefresh = false; 
        // 订阅事件
        ChatEventBus.on("setRefreshChanged", this.setRefresh.bind(this));
    }

    setSelectFileSearch(fileItem: DocumentListType) {
        const isExist = this.fileSearchList.some(item =>
            item.documentId === fileItem.documentId
        );

        if (!isExist) {
            this.fileSearchList = [...this.fileSearchList, fileItem];
            ChatEventBus.emit("fileSearchListChanged", this.fileSearchList);
        }
        console.log("fileSearchList", this.fileSearchList);
    }


    setData(data: AnswerType): void {
        this.answerData = data;
        this.id = data.id
    }

    getData(): AnswerType {
        return this.answerData;
    }

    // 点击刷新后更新当前content字段的值
    setRefreshContent(index: number) {
        if (index === -1) {
            // 对于历史会话，恢复原始内容
            if (this.isFromHistory && this.originalContent && this.originalResponse) {
                this.answerData.content = this.originalContent;
                this.answerData.response = this.originalResponse;
                // 恢复原始references
                if (this.originalReferences) {
                    this.references = this.originalReferences;
                }
                // 恢复原始citations
                if (this.originalCitations) {
                    this.citations = this.originalCitations;
                }
                // think
                if(this.originalThink) {
                    this.think = this.originalThink
                }
                // thinkingContent
                if(this.originalThinkingContent) {
                    this.thinkingContent = this.originalThinkingContent
                }
                // 恢复原始的深度思考标记（仅历史会话）
                if (this.originalIsDeepThink !== undefined) {
                    this.isDeepThink = this.originalIsDeepThink
                }
            }
            // 对于正常问答，index=-1时不做任何事，因为当前显示的就是最新内容
            return;
        }

        if (index >= 0 && index < this.refreshArr.length) {
            console.log('this.refreshArr[index] = ',this.refreshArr[index])
            const historyAnswer = this.refreshArr[index];
            this.answerData.content = historyAnswer.getData().content;
            this.answerData.response = historyAnswer.getData().response;
            this.references = historyAnswer.references;
            this.citations = historyAnswer.citations;
            this.think = historyAnswer.think
            this.thinkingContent = historyAnswer.thinkingContent
            // 切换历史页时，同步该页的深度思考标记，驱动标题展示（历史/正常问答通用）
            if (typeof historyAnswer.isDeepThink === 'boolean') {
                this.isDeepThink = historyAnswer.isDeepThink
            }
        }
    }


    // 更新设置当前content字段的值
    setContent(text: string, response: string = '', references: ReferencesType[],citations: any, documentList?: DocumentListType[], stepMode?: string, toolIndex?:string, think?:any): void {
        this.answerData.response += text;
        this.stepMode = stepMode || 'default value'
        this.toolIndex = toolIndex || 'default value'
        if (references) this.references = references
        if(citations) {
            this.citations = citations  
        }
        if(think) {
            this.think = think
        }
        if(stepMode === 'tool_complete'){
            const regex = new RegExp(`<div class="tool_selected">([\\s\\S]*?)</div>`);
            this.answerData.response = this.answerData.response.replace(regex,`<div class="tool_selected" style="display:none">$1</div>`)
        }
        else if(stepMode === 'planning_end'){
            const regexThink = new RegExp(`<div class="thinkcomplete">([\\s\\S]*?)</div>`);
            this.answerData.response = this.answerData.response.replace(regexThink,`<div class="thinkcomplete" style=" font-size: 14px;color: #6E6E78">$1</div>`)
        }

        if (documentList) {
            this.documentList = documentList

            //this.answerData.content = `Found ${this.documentList.length} files related to AI Now, sorted by relevance.`;
        } else {
            this.answerData.content = this.markdownParser.render(this.answerData.response);
        }
        if(stepMode && stepMode === "analyze_request"){
            console.log('analyze_request')
        }
        else if(stepMode && stepMode === "analyze_request"){
             console.log('searching')
        }

        if (stepMode && stepMode === "invoke_external_tool") {
            this.isAgent = response === "video_assistant" ? 'video':'email';
            if (response === "video_assistant") {
                this.answerData.content = `Video Creation Assistant can help you generate the videos you desire, either through text descriptions or by uploading images. With various preset configuration and post-editing features, it supports the quick generation of high-quality videos.`;
                this.answerData.response = `Video Creation Assistant can help you generate the videos you desire, either through text descriptions or by uploading images. With various preset configuration and post-editing features, it supports the quick generation of high-quality videos.`;
            }
            // if (response === "email_assistant") this.answerData.content = `Found ${this.documentList.length} files related to AI Now, sorted by relevance.`;
        } else {
            this.isAgent = ''; 
        }
    }

    // 加入刷新的数据
    setRefresh(data: Answer) {
        this.refreshArr.push(data)
    }

    // 处理聊天状态变化事件
    handleChatStatusChanged(status: ChatStatus) {
        this.chatStatusAnswer.value = status;
    }

    /**
     * 克隆当前对象，并返回一个新的 Answer 对象
     *
     * @returns 克隆后的 Answer 对象
     */
    clone(): Answer {
        const newAnswer = new Answer();
        // 深拷贝，确保内容不被后续更新影响
        newAnswer.answerData = {
            ...this.answerData,
            content: this.answerData.content,
            response: this.answerData.response
        };
        newAnswer.id = this.id;
        newAnswer.refreshArr = [];
        newAnswer.hasAddedToRefresh = false; // 重置标记
        newAnswer.isFromHistory = this.isFromHistory; // 复制历史标记
        newAnswer.isDeepThink = this.isDeepThink; // 复制深度思考标记（用于正常问答翻页标题）
        newAnswer.originalContent = this.originalContent; // 复制原始内容
        newAnswer.originalResponse = this.originalResponse; // 复制原始响应
        newAnswer.originalReferences = this.originalReferences; // 复制原始References
        newAnswer.originalCitations = this.originalCitations; // 复制原始Citations
        newAnswer.originalThinkingContent = this.originalThinkingContent; // 复制原始thinkingContent
        newAnswer.thinkingContent = this.thinkingContent; // 复制当前thinkingContent，用于正常问答切换分页更新标题
        newAnswer.tempContent = this.tempContent; // 复制临时内容
        newAnswer.tempResponse = this.tempResponse; // 复制临时响应
        newAnswer.tempThinkingContent = this.tempThinkingContent; // 复制临时thinkingContent
        if (Array.isArray(this.references)) {
            newAnswer.references = [...this.references];
        }
        // 复制当前的citations
        if (this.citations) {
            newAnswer.citations = JSON.parse(JSON.stringify(this.citations));
        }

        if (this.think) {
            newAnswer.think = this.think;
        }

        return newAnswer;
    }
}

// ChatController 类，作为控制器管理其他类的实例和公共数据
export class ChatController {
    welcomeConfig?: WelcomeType; // 欢迎语配置
    chatAction = reactive<ChatMessageType[]>([]); // 对话数据
    currentQueryId: string = ""; // 当前操作的提问的id
    currentChatStatus: ChatStatus = ChatStatus.DONE; // 当前对话状态默认结束状态
    isReGenerate: boolean = false; // 判读是否重新生成回答
    isWelcome: boolean = true; // 是否是欢迎语
    isWebSearch: boolean = false; // 是否是web搜索
    isDeeoThinking: boolean = false; // 是否是深度思考
    sessionId: string = ""; // 会话id
    // chatStatus: ChatStatus = ChatStatus.SUCCESS; // 当前对话状态默认结束状态
    chatStatus = reactive<{ value: ChatStatus }>({ value: ChatStatus.DONE });
    isRefresh: boolean = false; // 是否重新提问
    messageContainer: HTMLElement | null = null;
    history: historyItemType[] = []; // 历史记录数组，用于存储历史对话记录
    documentIds: Array<string> = [];
    knowledgeIds: Array<string> = [];
    uploadedFiles: { name: string; path: string; fileId: string; documentId: string }[] = []
    rawChunkData: StreamAnswerType[] = []

    constructor(sdk: ChatSDK, params?: WelcomeType) {
        if (params) this.welcomeConfig = params;
        setChatSDK(sdk); // 设置SDK
        ChatEventBus.on("setQueryValue", this.setQueryValue.bind(this));
        ChatEventBus.on("listenMessage", this.listenMessage.bind(this));
        this.sessionId = uuidv4(); // 生成会话id
        this.chatStatus.value = ChatStatus.DONE;

    }

    getChatStatus(): ChatStatus {
        return this.chatStatus.value;
    }


    setWebSearch(type: boolean): void {
        this.isWebSearch = type;
    }
    setDeepThinking(type: boolean): void {
        this.isDeeoThinking = type;
    }

    setRegenerate(type: boolean) { 
        this.isReGenerate = type
    }

    getWelcomeConfig(): WelcomeType | undefined {
        return this.welcomeConfig;
    }

    /**
     * 设置欢迎信息的显示状态。此方法用于控制是否在界面上展示欢迎信息。
     *
     * @param type 布尔值，用于设置欢迎信息的类型
     *        - true：显示欢迎信息
     *        - false：不显示欢迎信息
     */
    setWelcomeType(type: boolean): void {
        this.isWelcome = type;
    }

    setMessageContainerRef(container: HTMLElement | null) {
        this.messageContainer = container;
    }

    async scrollToBottom(num: number = 0) {
        await nextTick();
        setTimeout(() => {
            if (this.messageContainer) {
                this.messageContainer.scrollTo({
                    top: this.messageContainer.scrollHeight,
                });
            }
        }, num)
    }

    // 提问的时候触发进行query和answer的数据生成
    setQueryValue(value: string): void {
        try {
            // 清空原始chunk数据
            this.clearRawChunkData();
            
            // 重置重新生成标记，确保新问答不是刷新操作
            this.isReGenerate = false;
            this.isRefresh = false;
            const queryId = uuidv4();
            const timestamp = Date.now();
            const queryData: QuestionType = {
                id: queryId,
                content: value,
                timestamp,
                type: ChatComponentType.QUERY,
                files: this.documentIds.map(id => {
                    const file = this.uploadedFiles.find(f => f.documentId === id);
                    return {
                        name: file?.name || '',
                        path: file?.path || '',
                        fileId: file?.fileId || '',
                        documentId: id
                    };
                })
            };
            const answerData: AnswerType = {
                id: uuidv4(),
                questionId: queryId,
                content: "",
                response: "",
                timestamp,
                type: ChatComponentType.ANSWER,
                queryData
            };
            this.currentQueryId = queryId;
            const question = new Question();
            question.setData(queryData);

            const answer = new Answer();
            answer.isDeepThink = this.isDeeoThinking
            answer.isFromHistory = false // 标记新创建的Answer对象不是从历史加载
            answer.setData(answerData);

            if (this.documentIds.length > 0) {
                this.documentIds.forEach(id => {
                    const file = this.uploadedFiles.find(f => f.documentId === id);
                    if (file) {
                        answer.fileSearchList.push({
                            documentId: id,
                            documentName: file.name,
                            knowledgeId: file.fileId
                        });
                    }
                });
            }

            // 推入chatAction数组
            this.chatAction.push(question, answer);
            this.send_message(value);

            // 清空documentIds和uploadedFiles，确保下次问答不携带之前的文档
            this.documentIds = [];
            this.uploadedFiles = [];
        } catch (error) {
            console.warn("UUID NO NO NO", error);
        }
    }

    // 开始重新生成回答
    startRegenerate(data: AnswerType): void {
        this.currentQueryId = data.questionId;
        this.isRefresh = true;
        this.setStatusType(ChatStatus.START);
        const currentAnswer = this.getCurrentOperatingObject() as Answer;
        currentAnswer.regen = true;
        currentAnswer.answerData.content = "";
        currentAnswer.answerData.response= "";
        // 重置hasAddedToRefresh标记，以便在clickRefresh中可以保存当前版本
        currentAnswer.hasAddedToRefresh = false;
        const queryContent = data.queryData?.content || "";
        if (data.queryData && this.documentIds.length > 0) {
            data.queryData.files = this.documentIds.map(id => {
                const file = this.uploadedFiles.find(f => f.documentId === id);
                return {
                    name: file?.name || '',
                    path: file?.path || '',
                    fileId: file?.fileId || '',
                    documentId: id
                };
            });
        }
        
        this.send_message(queryContent);
        this.scrollToBottom();
    }

    private isQuestion(item: any): item is { questionData: QuestionType } {
        return 'questionData' in item;
    }

    private isAnswer(item: any): item is { answerData: AnswerType } {
        return 'answerData' in item;
    }

    private filterChatAction(): historyItemType[] {
        const historyData = this.chatAction.map(item => {
            if (!item) return null;

            // 处理问题数据
            if (this.isQuestion(item)) {
                return {
                    role: item.questionData.type || 'unknown',
                    content: item.questionData.content,
                };
            }

            // 处理回答数据
            if (this.isAnswer(item)) {
                const lastRefresh = item.refreshArr.length > 0
                    ? item.refreshArr[item.refreshArr.length - 1]
                    : item;
                console.log(lastRefresh, 'lastRefreshlastRefreshlastRefreshlastRefresh')
                return {
                    role: item.answerData.type || 'unknown',
                    content: lastRefresh.answerData.response || '',
                };
            }
            return null;
        }).filter((item): item is historyItemType => item !== null);
        if (this.chatAction.length > 2) {
            return historyData.slice(0, -2);
        }
        return []
    }

    // 放入文件Ids
    setDocumentIds(params: Array<string>): void {
        this.documentIds = params
    }

    // 放入知识库Ids
    setKnowledgeIds(params: Array<string>): void {
        this.knowledgeIds = params
    }


    // 发送消息到服务端进行回答生成
    private send_message(text: string): void {
        console.log('isReGenerate =',this.isReGenerate)
        const history = this.filterChatAction();
        // 调用中间件方法去发送消息
        this.isWebSearch = localStorage.getItem('webSearchChecked') === "true" ? true :false;
        sendServiceMsg(text, this.sessionId, this.isDeeoThinking, this.isWebSearch, this.isReGenerate, history, this.documentIds, this.knowledgeIds);
        this.setStatusType(ChatStatus.START);
        this.scrollToBottom()
        console.log("fasongixaoxi")
    }

    setStatusType(type: ChatStatus): void {
        this.chatStatus.value = type;
        const currentAnswer = this.getCurrentOperatingObject() as Answer;
        currentAnswer?.handleChatStatusChanged(type)
    }

    /**
     * 获取当前操作的 Answer 对象
     * @returns Answer | undefined
     */
    private getCurrentOperatingObject(): Answer | undefined {
        for (const item of this.chatAction) {
            // 确保是 Answer 类型
            if (item instanceof Answer) {
                const answerData = item.getData();
                // 检查 questionId 是否匹配
                if (answerData.questionId === this.currentQueryId) {
                    item.isDeepThink = this.isDeeoThinking
                    return item;
                }
            }
        }
        return undefined;
    }

    // 停止生成回答
    stopRegenerate() {
        this.chatStatus.value = ChatStatus.DONE;
        const currentAnswer = this.getCurrentOperatingObject() as Answer;
        currentAnswer?.handleChatStatusChanged(ChatStatus.DONE)
        stopServiceMsg();
    }

    // 获取原始chunk数据
    getRawChunkData(): StreamAnswerType[] {
        return [...this.rawChunkData]; // 返回数组副本，避免外部修改
    }

    // 清空原始chunk数据
    clearRawChunkData(): void {
        this.rawChunkData = [];
    }

    private async listenMessage(data: StreamAnswerType): Promise<void> {
        
        const { status, content, response, references,citations, documentList, stepMode, toolIndex, meta } = data;

        this.rawChunkData.push(data);

        this.currentChatStatus = status as ChatStatus;
        this.chatStatus.value = status as ChatStatus;
        const currentAnswer = this.getCurrentOperatingObject() as Answer;
        currentAnswer?.handleChatStatusChanged(status)
        if (currentAnswer) {
            if(meta){
            const {isDeepThink} = meta
            if(isDeepThink)
                currentAnswer.isDeepThink = isDeepThink
            }
            if(stepMode === 'tool_complete'){
                currentAnswer.toolType.push({
                    toolIndex: toolIndex,
                    references: references
            })
            }
            // 处理内置的 ChatStatus 枚举值
            if (status === ChatStatus.ONGOING || status === ChatStatus.SUCCESS || 
                status === ChatStatus.FAILURE) {
                currentAnswer.setContent(content, response, references,citations, documentList, stepMode,toolIndex);
                // 如果是success状态且是重新生成
                if (status === ChatStatus.SUCCESS && this.isRefresh) {
                    // 如果是正常问答（非历史会话），保存最新生成的内容
                    if (!currentAnswer.isFromHistory) {
                        currentAnswer.originalContent = currentAnswer.answerData.content;
                        currentAnswer.originalResponse = currentAnswer.answerData.response;
                        // 保存最新的references
                        currentAnswer.originalReferences = [...currentAnswer.references];
                        // 保存最新的citations
                        currentAnswer.originalCitations = JSON.parse(JSON.stringify(currentAnswer.citations));
                        currentAnswer.originalThink = currentAnswer.think
                        currentAnswer.originalThinkingContent = currentAnswer.thinkingContent
                    }
                    
                    // 标记重置，以便下次重新生成时可以再次保存
                    currentAnswer.hasAddedToRefresh = false;
                }
            } else if (status === ChatStatus.DONE) {
                if (this.isRefresh && !currentAnswer.isFromHistory) {
                    // 重置重新生成标记
                    this.isRefresh = false
                }
            } else if (typeof status === 'string' && status === ChatStatus.FAIL) {
                // 单独处理字符串 "fail" 状态
                currentAnswer.setContent(content, response, references,citations, documentList, stepMode);
            }

            const index = this.chatAction.findIndex(
                (item) => item.id === currentAnswer.id
            );
            if (index !== -1) {
                this.chatAction[index] = currentAnswer as ChatMessageType;
            }
        }
        await this.scrollToBottom();
    }
}