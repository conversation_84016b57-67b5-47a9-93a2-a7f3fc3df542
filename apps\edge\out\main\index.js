"use strict";
const electron = require("electron");
const utils = require("@electron-toolkit/utils");
const fs = require("fs");
const path = require("path");
const log = require("electron-log/main");
require("child_process");
const promises = require("fs/promises");
const worker_threads = require("worker_threads");
function _interopNamespaceDefault(e) {
  const n = Object.create(null, { [Symbol.toStringTag]: { value: "Module" } });
  if (e) {
    for (const k in e) {
      if (k !== "default") {
        const d = Object.getOwnPropertyDescriptor(e, k);
        Object.defineProperty(n, k, d.get ? d : {
          enumerable: true,
          get: () => e[k]
        });
      }
    }
  }
  n.default = e;
  return Object.freeze(n);
}
const fs__namespace = /* @__PURE__ */ _interopNamespaceDefault(fs);
const path__namespace = /* @__PURE__ */ _interopNamespaceDefault(path);
var WinState = /* @__PURE__ */ ((WinState2) => {
  WinState2["Max"] = "max";
  WinState2["Min"] = "min";
  WinState2["Normal"] = "normal";
  return WinState2;
})(WinState || {});
function getResourcesPath(fullPath) {
  return fullPath.replace("app.asar", "app.asar.unpacked");
}
const getCurrentUTCDateStr = () => {
  const currentDate = /* @__PURE__ */ new Date();
  return currentDate.getFullYear().toString() + currentDate.getUTCMonth().toString() + currentDate.getUTCDate().toString();
};
const checkFileExistsSync = (filePath) => {
  try {
    fs.statSync(filePath);
    return true;
  } catch (error) {
    return false;
  }
};
const createFile = (path2, initStr = "", force = false) => {
  try {
    if (force) {
      fs.writeFileSync(path2, initStr);
    } else {
      const isExist = checkFileExistsSync(path2);
      if (!isExist) {
        fs.writeFileSync(path2, initStr);
      }
    }
  } catch (err) {
    console.error(err);
  }
};
const getFile = async (path2) => {
  try {
    const data = await fs.promises.readFile(path2, "utf-8");
    return data;
  } catch (error) {
    console.log(error);
    return "";
  }
};
const getSyncFile = (path2) => {
  try {
    const data = fs.readFileSync(path2, "utf-8");
    return data;
  } catch (error) {
    console.log(error);
    return "";
  }
};
function parseQuery(queryStr, options = { multiple: false }) {
  function splitKeyValue(pair) {
    const eqIndex = pair.indexOf("=");
    if (eqIndex === -1) {
      return [pair, null];
    }
    return [pair.substring(0, eqIndex), pair.substring(eqIndex + 1)];
  }
  function handleDuplicateKeys(result2, key, value, isMultiple) {
    if (!result2.hasOwnProperty(key)) {
      result2[key] = isMultiple ? [value] : value;
      return;
    }
    const existing = result2[key];
    if (isMultiple) {
      Array.isArray(existing) ? existing.push(value) : result2[key] = [existing, value];
    } else {
      result2[key] = value;
    }
  }
  const result = {};
  if (typeof queryStr !== "string" || queryStr.trim() === "") {
    return result;
  }
  const query = queryStr.replace(/^[?#]/, "");
  const pairs = query.split("&");
  for (const pair of pairs) {
    let [key, value] = splitKeyValue(pair);
    key = decodeURIComponent(key.replace(/\+/g, " "));
    value = value !== null ? decodeURIComponent(value.replace(/\+/g, " ")) : "";
    handleDuplicateKeys(result, key, value, options.multiple);
  }
  return result;
}
function getFilenameFromPath(filePath) {
  return path.basename(filePath);
}
class Logger {
  constructor(logPath, id = "main") {
    log.initialize();
    this.logger = log.create({ logId: id });
    const filePath = path.join(logPath, this.logger.logId + getCurrentUTCDateStr() + ".log");
    createFile(filePath);
    if (this.logger.transports?.file) {
      this.handleFileTransport(filePath, this.logger.transports.file);
    }
  }
  logger;
  createScope(scope) {
    return this.logger.scope(scope);
  }
  handleFileTransport(logPath, file) {
    file.resolvePathFn = () => {
      return logPath;
    };
  }
}
new Logger(electron.app.getPath("logs"), "main");
const logCombine = (path2, id) => {
  const logger = new Logger(path2, id);
  const { info, error, warn, silly, verbose, debug } = logger.logger.functions;
  Object.assign(console, {
    info,
    error,
    warn,
    silly,
    verbose,
    debug
  });
};
function openWebSite(url) {
  return electron.shell.openExternal(url);
}
function setBounds(win, bounds) {
  win.setBounds(bounds);
}
function getMoveWinFunc(win) {
  let winStartPosition = { x: 0, y: 0 };
  let mouseStartPosition = { x: 0, y: 0 };
  let movingInterval = null;
  electron.screen.getAllDisplays();
  return {
    curWin: win,
    execFunc: (canMoving) => {
      if (canMoving) {
        const { x, y, width, height } = win.getContentBounds();
        winStartPosition = { x, y };
        mouseStartPosition = electron.screen.getCursorScreenPoint();
        if (movingInterval) {
          clearInterval(movingInterval);
        }
        movingInterval = setInterval(() => {
          const cursorPosition = electron.screen.getCursorScreenPoint();
          const x2 = winStartPosition.x + cursorPosition.x - mouseStartPosition.x;
          const y2 = winStartPosition.y + cursorPosition.y - mouseStartPosition.y;
          win.setBounds({ x: x2, y: y2, width, height });
        }, 15);
      } else {
        movingInterval && clearInterval(movingInterval);
        movingInterval = null;
      }
    }
  };
}
function handleWinShow(win) {
  if (win.isMinimized()) {
    win.restore();
  }
  win.show();
  return win;
}
function broadcastToWindow(win, eventName, data) {
  win.webContents.send(eventName, data);
}
const isMax = (win) => {
  const bounds = win.getBounds();
  const cur = electron.screen.getDisplayNearestPoint({
    x: bounds.x + 8,
    y: bounds.y + 8
  });
  console.log("=============");
  console.log(bounds);
  console.log("=>>>>>>>>>>>>");
  console.log(cur);
  return bounds.height >= cur.workArea.height && bounds.width >= cur.workArea.width && bounds.x === cur.workArea.x - 8 && bounds.y === cur.workArea.y - 8;
};
class ConfigClass {
  initPromise = null;
  constructor() {
  }
  init() {
    if (!this.initPromise) {
      this.initPromise = this.initConfig();
    }
    return this.initPromise;
  }
  async initConfig() {
    const configExist = checkFileExistsSync(this.configPath);
    let existingConfig = {};
    if (configExist) {
      try {
        const file = await getFile(this.configPath);
        existingConfig = JSON.parse(file || "{}");
      } catch (e) {
        existingConfig = {};
      }
    }
    const newConfig = { ...existingConfig, ...this.defaultConfig };
    this.curConfig = newConfig;
    try {
      if (fs.existsSync(this.configPath)) {
        fs.unlinkSync(this.configPath);
      }
      await createFile(this.configPath, JSON.stringify(newConfig, null, 2));
    } catch (e) {
      this.curConfig = this.defaultConfig;
    }
  }
  defaultConfig = {
    // authServer: `${domain}:9080/auth`,
    // edgeServer: `${domain}:9070/ainow-user-acl-api`,
    // kbFileServer: `${domain}:9070/lr-kb-service-api`,
    // modelServer: `${domain}:8099/api/v1/flow/c7667aa7-984a-4b49-a3b6-295e82dd705a`,
    // historyServer: `${domain}:8102/api/v1`,
    // chunkServer: `${domain}:8099/api/v1`
    authServer: "http://*************:9080/auth",
    edgeServer: "http://*************:9070/ainow-user-acl-api",
    kbFileServer: "http://*************:9070/lr-kb-service-api",
    modelServer: "http://*************:8099/api/v1/flow/c7667aa7-984a-4b49-a3b6-295e82dd705a",
    historyServer: "http://*************:8102/api/v1",
    chunkServer: "http://*************:8099/api/v1"
  };
  curConfig = {};
  configPath = path.join(electron.app.getPath("userData"), "config.json");
  async getConfig(needUpDate = false) {
    await this.init();
    if (needUpDate) {
      await this.updateConfig();
    }
    return this.curConfig;
  }
  async updateConfig() {
    const file = getSyncFile(this.configPath);
    this.curConfig = JSON.parse(file);
  }
}
const Config = new ConfigClass();
function IpcRegister(win) {
  const beforeMaxMounds = win.getBounds();
  function handleIpcResponse(success, res) {
    const timestamp = Date.now();
    return { success, res, timestamp };
  }
  const moveHandler = getMoveWinFunc(win);
  electron.ipcMain.on("setWinBounds", (e, bound) => {
    if (!win.isDestroyed()) {
      setBounds(win, bound);
    }
  });
  electron.ipcMain.on("window:move", (e, canMoving) => {
    moveHandler.execFunc(canMoving);
  });
  electron.ipcMain.handle("getBounds", () => {
    const res = win.getBounds();
    return handleIpcResponse(!!res, res);
  });
  electron.ipcMain.on("setIgnoreMouseEvents", (e, ignore, option) => {
    if (!win.isDestroyed()) {
      win.setIgnoreMouseEvents(ignore, option);
    }
  });
  electron.ipcMain.handle("openWebSite", (e, url) => {
    return openWebSite(url);
  });
  electron.ipcMain.on("setWindowBlur", () => {
    if (!win.isDestroyed()) {
      win.blur();
    }
  });
  electron.ipcMain.on("winMax", () => {
    if (!win.isDestroyed()) {
      Object.assign(beforeMaxMounds, win.getBounds());
      win.maximize();
    }
  });
  electron.ipcMain.on("winRestore", () => {
    console.log("winRestore");
    if (!win.isDestroyed()) {
      win.setBounds(beforeMaxMounds);
    }
  });
  electron.ipcMain.on("winMin", () => {
    if (!win.isDestroyed()) {
      win.minimize();
    }
  });
  electron.ipcMain.on("winClose", () => {
    win.close();
  });
  electron.ipcMain.handle("getWinState", () => {
    let state = WinState.Normal;
    if (isMax(win)) {
      state = WinState.Max;
    } else if (win.isMinimized()) {
      state = WinState.Min;
    }
    return state;
  });
  electron.ipcMain.handle("getConfig", (e, needUpDate) => {
    return Config.getConfig(needUpDate);
  });
  electron.ipcMain.handle("print-to-pdf", async () => {
    if (!win || win.isDestroyed()) {
      throw new Error("Browser window is not available");
    }
    try {
      return await win.webContents.printToPDF({
        printBackground: true,
        pageSize: "A4",
        landscape: false
      });
    } catch (err) {
      console.error("PDF生成失败:", err);
    }
  });
  electron.ipcMain.handle("save-pdf", async (event, { buffer, path: path2 }) => {
    try {
      await fs__namespace.promises.writeFile(path2, buffer);
      return { success: true };
    } catch (err) {
      console.error("文件保存失败:", err);
      throw err;
    }
  });
  electron.ipcMain.handle("show-save-dialog", (_, options) => {
    return electron.dialog.showSaveDialog(win, options);
  });
  electron.ipcMain.handle("save-file", async (_, { fileName, data }) => {
    const downloadsPath = electron.app.getPath("downloads");
    const filePath = path.join(downloadsPath, fileName);
    return new Promise((resolve, reject) => {
      fs__namespace.writeFile(filePath, Buffer.from(data), (err) => {
        if (err) {
          reject(`save file error: ${err.message}`);
        } else {
          resolve(filePath);
        }
      });
    });
  });
}
const scheme = "ainow.row";
const toLoginWithCode = (win, argv) => {
  const codeStr = argv.find((arg) => arg.includes(scheme));
  console.info("process.argv", argv);
  if (codeStr) {
    const code = parseQuery(codeStr.slice(codeStr.indexOf("?") + 1));
    console.info("LID Code", code);
    broadcastToWindow(win, "toLoginWithCode", code);
  }
  return codeStr;
};
const setProtocolScheme = () => {
  const args = [];
  if (!electron.app.isPackaged) {
    args.push(path.resolve(process.argv[1]));
  }
  args.push("--");
  try {
    electron.app.setAsDefaultProtocolClient(scheme, process.execPath, args);
  } catch (error) {
    console.error(error);
  }
};
function createMainWin() {
  const mainWindow = new electron.BrowserWindow({
    width: 1100,
    height: 800,
    minHeight: 600,
    minWidth: 1100,
    frame: false,
    hasShadow: true,
    useContentSize: true,
    roundedCorners: true,
    transparent: true,
    resizable: true,
    show: false,
    // 先不显示窗口
    title: "Lenovo Team AI",
    // backgroundColor: '#000000', // 必须为 HEX 透明色
    webPreferences: {
      webSecurity: false,
      // 关键配置：禁用同源策略
      zoomFactor: 1,
      preload: path.join(__dirname, "../preload/index.js"),
      sandbox: false
    }
  });
  global.mainWindow = mainWindow;
  const primaryDisplay = electron.screen.getPrimaryDisplay();
  const { width, height } = primaryDisplay.workAreaSize;
  const windowBounds = mainWindow.getBounds();
  const x = Math.floor((width - windowBounds.width) / 2);
  const y = Math.floor((height - windowBounds.height) / 2);
  mainWindow.setBounds({ x, y, width: windowBounds.width, height: windowBounds.height });
  mainWindow.show();
  mainWindow.on("will-resize", (e, newBounds) => {
    console.warn(e, newBounds, "will-resize");
  });
  mainWindow.webContents.setWindowOpenHandler((details) => {
    electron.shell.openExternal(details.url);
    return { action: "deny" };
  });
  if (utils.is.dev && process.env["ELECTRON_RENDERER_URL"]) {
    mainWindow.loadURL(process.env["ELECTRON_RENDERER_URL"] + "/#/main");
  } else {
    mainWindow.loadFile(path.join(__dirname, "../renderer/index.html"), { hash: "main" });
  }
  mainWindow.webContents.on("did-finish-load", () => {
    console.log("@@@###@@@");
    mainWindow.webContents.session.setPermissionRequestHandler(
      (webContents, permission, callback) => {
        console.log("###@@@");
        if (permission === "media") {
          return callback(true);
        }
        return callback(false);
      }
    );
  });
  electron.screen.on("display-metrics-changed", () => {
    if (!mainWindow.isDestroyed()) {
      const { width: width2, height: height2 } = mainWindow.getBounds();
      console.log("Display metrics changed, updating window constraints...", width2, height2);
      mainWindow.setBounds({
        width: width2,
        height: height2
      });
    }
  });
  mainWindow.on("blur", () => {
  });
  mainWindow.on("show", () => {
    mainWindow.focus();
    mainWindow.webContents.send("visible-focus");
  });
  mainWindow.on("ready-to-show", () => {
  });
  mainWindow.on("maximize", () => {
    if (!mainWindow.isDestroyed()) {
      const bound = mainWindow.getBounds();
      mainWindow.setBounds({
        width: bound.width + 16,
        height: bound.height + 16,
        x: bound.x - 8,
        y: bound.y - 8
      });
    }
  });
  return mainWindow;
}
const PrivateKBSource = "bccd276b-7b52-478c-a8b3-b9cb91012165";
var FileStatus = /* @__PURE__ */ ((FileStatus2) => {
  FileStatus2["PENDING"] = "Waiting";
  FileStatus2["UPLOADING"] = "Uploading";
  FileStatus2["SUCCESS"] = "success";
  FileStatus2["FAILED"] = "Failed";
  FileStatus2["CONFLICT"] = "conflict";
  FileStatus2["PAUSED"] = "Paused";
  return FileStatus2;
})(FileStatus || {});
var SyncStatus = /* @__PURE__ */ ((SyncStatus2) => {
  SyncStatus2["IDLE"] = "idle";
  SyncStatus2["SYNCING"] = "syncing";
  SyncStatus2["COMPLETED"] = "completed";
  SyncStatus2["FAILED"] = "failed";
  SyncStatus2["CONFLICT"] = "conflict";
  return SyncStatus2;
})(SyncStatus || {});
const SyncEvents = {
  // 同步生命周期事件
  SYNC_STARTED: "sync-started",
  SYNC_COMPLETED: "sync-completed",
  SYNC_FAILED: "sync-failed",
  // 文件操作事件
  FILE_LIST_UPDATED: "file-list-updated",
  // 更新列表
  // 上传事件
  ONE_UPLOAD_COMPLETED: "one-upload-completed",
  ONE_UPLOAD_FAILED: "one-upload-failed",
  CHAT_UPLOAD_COMPLETED: "chat-upload-completed",
  CHAT_UPLOAD_FAILED: "chat-upload-failed",
  // 状态事件
  PROGRESS_UPDATE: "progress-update",
  CONFLICT_DETECTED: "conflict-detected",
  FILE_PROGRESS_UPDATE: "file-progress-update",
  // 接收到worker消息事件
  START_UPLOAD_TO_WORKER: "start-upload-to-worker",
  WORKER_MESSAGE: "worker-message",
  RECEIVE_HEARTBEAT: "receive-heartbeat"
  // 接收到心跳消息
};
const IpcChannels = {
  // 更新上传API地址
  UPDATE_UPLOAD_API_URL: "fileSync:updateUploadApiUrl",
  UPDATE_UPLOAD_API_INFO: "fileSync:updateUploadApiInfo",
  // 文件选择相关
  SELECT_FILES: "fileSync:selectFiles",
  // 统一的文件选择通道，支持指定source参数
  GET_FILES: "fileSync:getFiles",
  GET_FILES_BY_PATH: "file-sync:get-files-by-path",
  SELECT_CUSTOM_PATH_FILES_UPLOAD: "fileSync:selectCustomPathFilesUpload",
  // 聊天文件选择相关
  CHOOSE_FILES: "fileSync:chooseFiles",
  DROP_FILES: "fileSync:dropFiles",
  // 聊天文件拖拽上传
  // 上传相关
  START_UPLOAD: "fileSync:startUpload",
  CANCEL_UPLOAD: "fileSync:cancelUpload",
  PAUSE_UPLOADING_TASK: "fileSync:pauseUploadingTask",
  DELETE_UPLOADING_TASK: "fileSync:deleteUploadingTask",
  DELETE_FILES: "fileSync:deleteFiles",
  // 渲染进程状态相关
  RENDERER_READY: "fileSync:rendererReady",
  CHECK_RENDERER_STATUS: "fileSync:checkRendererStatus"
};
class FileStateStore {
  storePath;
  constructor() {
    this.storePath = path__namespace.join(electron.app.getPath("userData"), "fileSync.json");
  }
  /**
   * 从文件加载状态
   * @returns 加载的文件列表
   */
  loadStates() {
    try {
      if (fs__namespace.existsSync(this.storePath)) {
        const data = fs__namespace.readFileSync(this.storePath, "utf-8");
        const files = JSON.parse(data);
        console.log(`已加载 ${files.length} 个文件状态记录`);
        return files;
      }
    } catch (error) {
      console.error("加载文件状态失败:", error);
    }
    return [];
  }
  /**
   * 保存状态到文件
   * @param files 要保存的文件列表
   */
  saveStates(files) {
    try {
      const pendingFiles = files.filter((file) => file.status !== FileStatus.SUCCESS);
      fs__namespace.writeFileSync(this.storePath, JSON.stringify(pendingFiles, null, 2), "utf-8");
    } catch (error) {
      console.error("保存文件状态失败:", error);
    }
  }
}
class FileHandlers {
  syncManager;
  constructor(_syncManager) {
    this.syncManager = _syncManager;
    this.registerHandlers();
  }
  /**
   * 注册IPC处理函数
   */
  registerHandlers() {
    electron.ipcMain.handle(IpcChannels.UPDATE_UPLOAD_API_URL, (_, { uploadApiUrl }) => {
      return this.syncManager.updateUploadApiUrl(uploadApiUrl);
    });
    electron.ipcMain.handle(IpcChannels.UPDATE_UPLOAD_API_INFO, (_, { apiToken, uploadApiUrl }) => {
      return this.syncManager.updateUploadApiInfo({
        apiToken,
        uploadApiUrl
      });
    });
    electron.ipcMain.handle(
      IpcChannels.SELECT_FILES,
      async (_, {
        source = PrivateKBSource,
        isLimitFileCount,
        userIdForUpload,
        resourceId,
        resourceType
      } = {}) => {
        return await this.syncManager.selectFiles({
          source,
          isLimitFileCount,
          userIdForUpload,
          resourceId,
          resourceType
        });
      }
    );
    electron.ipcMain.handle(
      IpcChannels.CHOOSE_FILES,
      async (_, { source, userIdForUpload, resourceId, resourceType, remainingSlots } = {}) => {
        return await this.syncManager.chooseFiles(
          source,
          userIdForUpload,
          resourceId,
          resourceType,
          void 0,
          remainingSlots
        );
      }
    );
    electron.ipcMain.handle(IpcChannels.DROP_FILES, async (_, { source, result } = {}) => {
      return await this.syncManager.fileUpload(source, result);
    });
    electron.ipcMain.handle(IpcChannels.GET_FILES, () => {
      return this.syncManager.getPendingFiles();
    });
    electron.ipcMain.handle(IpcChannels.START_UPLOAD, async (_, { fileIds }) => {
      try {
        const result = await this.syncManager.startUpload(fileIds);
        return result;
      } catch (error) {
        return { success: false, error: error.message || String(error) };
      }
    });
    electron.ipcMain.handle(IpcChannels.CANCEL_UPLOAD, async (_, { fileIds }) => {
      return this.syncManager.puauseUpload(fileIds);
    });
    electron.ipcMain.handle(IpcChannels.PAUSE_UPLOADING_TASK, async (_, { fileIds }) => {
      return this.syncManager.puauseUploadingTask(fileIds);
    });
    electron.ipcMain.handle(IpcChannels.DELETE_UPLOADING_TASK, async (_, { fileIds }) => {
      return this.syncManager.deleteUploadingTask(fileIds);
    });
    electron.ipcMain.handle(IpcChannels.DELETE_FILES, async (_, { fileIds }) => {
      this.syncManager.deleteFiles(fileIds);
      return { success: true, count: 0 };
    });
    electron.ipcMain.handle(IpcChannels.GET_FILES_BY_PATH, async (_, { folderPath }) => {
      try {
        const files = await this.syncManager.getFilesByPath(folderPath);
        return files;
      } catch (error) {
        return [];
      }
    });
    electron.ipcMain.handle(
      IpcChannels.SELECT_CUSTOM_PATH_FILES_UPLOAD,
      async (_, { folderPath, source, resourceId, resourceType }) => {
        return await this.syncManager.selectCustomPathFilesUpload(
          folderPath,
          source,
          resourceId,
          resourceType
        );
      }
    );
  }
  /**
   * 清理IPC处理函数
   */
  cleanup() {
    electron.ipcMain.removeHandler(IpcChannels.START_UPLOAD);
    electron.ipcMain.removeHandler(IpcChannels.GET_FILES);
    electron.ipcMain.removeHandler(IpcChannels.CANCEL_UPLOAD);
    electron.ipcMain.removeHandler(IpcChannels.DELETE_FILES);
    electron.ipcMain.removeHandler(IpcChannels.GET_FILES_BY_PATH);
  }
  async handleChooseFiles(source) {
    return await this.syncManager.chooseFiles(source);
  }
}
let lastFileSelectTime = 0;
class FileSyncManager {
  // 添加图片扩展名数组
  static IMAGE_EXTENSIONS = ["jpg", "jpeg", "png", "bmp"];
  // 添加默认过滤器作为类常量
  static DEFAULT_FILTERS = [
    {
      name: "All Files",
      extensions: [
        "pdf",
        "ppt",
        "pptx",
        "doc",
        "docx",
        "txt",
        "xls",
        "xlsx",
        "jpg",
        "jpeg",
        "png",
        "bmp"
      ]
    },
    { name: "PDF Files", extensions: ["pdf"] },
    { name: "PowerPoint Presentations", extensions: ["ppt", "pptx"] },
    { name: "Word Documents", extensions: ["doc", "docx"] },
    { name: "Text Files", extensions: ["txt"] },
    { name: "Excel", extensions: ["xls", "xlsx"] },
    { name: "Images", extensions: FileSyncManager.IMAGE_EXTENSIONS }
  ];
  static MAX_FILE_COUNT = 10;
  // 最大文件选择数量
  static MAX_IMAGE_SIZE = 50 * 1024 * 1024;
  // 图片最大50MB
  static MAX_OTHER_SIZE = 2 * 1024 * 1024 * 1024;
  // 其他文件最大2GB
  status = SyncStatus.IDLE;
  progress = {
    total: 0,
    processed: 0,
    deleted: 0,
    conflicts: 0
  };
  pendingFiles = [];
  // 待上传文件列表
  fileStateStore;
  // 文件列表状态存储
  currentWorker;
  currentChatWorker;
  currentFileId;
  token;
  userIdForUpload = "";
  uploadApiUrl = "";
  // 上传API地址
  handlers;
  mainWindow;
  // 渲染进程就绪状态管理
  isRendererReady = false;
  pendingAutoUpload = false;
  rendererReadyCheckInterval;
  constructor(mainWindow) {
    this.mainWindow = mainWindow;
    this.fileStateStore = new FileStateStore();
    this.handlers = new FileHandlers(this);
    this.loadHistoryState();
    this.setupIpcHandlers();
    setInterval(() => {
      this.emitEvent(SyncEvents.RECEIVE_HEARTBEAT, {
        timestamp: (/* @__PURE__ */ new Date()).toLocaleString("zh-CN", {
          year: "numeric",
          month: "2-digit",
          day: "2-digit",
          hour: "2-digit",
          minute: "2-digit",
          second: "2-digit"
        })
      });
    }, 5e3);
  }
  /**
   * 加载历史状态
   */
  loadHistoryState() {
    this.pendingFiles = this.fileStateStore.loadStates();
    console.log(`加载历史文件状态: ${this.pendingFiles.length} 个上传文件`);
  }
  /**
   * 设置 IPC 监听器
   */
  setupIpcHandlers() {
    electron.ipcMain.handle(IpcChannels.RENDERER_READY, () => {
      this.isRendererReady = true;
      if (this.rendererReadyCheckInterval) {
        clearInterval(this.rendererReadyCheckInterval);
        this.rendererReadyCheckInterval = void 0;
      }
      if (this.pendingAutoUpload) {
        this.pendingAutoUpload = false;
        this.executeAutoUpload();
      }
      return { success: true };
    });
    electron.ipcMain.handle(IpcChannels.CHECK_RENDERER_STATUS, () => {
      return {
        isReady: this.isRendererReady,
        hasPendingFiles: this.pendingFiles.length > 0
      };
    });
  }
  /**
   * 通过路径返回文件列表
   */
  async getFilesByPath(folderPath, source, resourceId, resourceType) {
    const fileInfos = [];
    try {
      const filePaths = await this.getFilesRecursively(folderPath, FileSyncManager.DEFAULT_FILTERS);
      for (const filePath of filePaths) {
        const stats = await fs__namespace.promises.stat(filePath);
        const parsedPath = path__namespace.parse(filePath);
        const fileInfo = {
          path: filePath,
          name: parsedPath.base,
          size: stats.size,
          lastModified: stats.mtimeMs,
          isDirectory: false,
          // 因为 getFilesRecursively 只返回文件
          source,
          // PrivateKBSource, // 默认为 PKB，可以根据需要修改
          fileId: `${source}_${parsedPath.base}_${Date.now()}`,
          status: FileStatus.PENDING,
          resourceId,
          // 需要使用PrivateKB 的knowledgeBaseId
          resourceType
        };
        fileInfos.push(fileInfo);
      }
    } catch (error) {
      console.error("获取文件列表错误:", error);
    }
    return fileInfos;
  }
  /**
   * 检查文件是否符合过滤条件
   */
  isFileMatchFilter(fileName, filters) {
    if (!filters) return true;
    const ext = path__namespace.extname(fileName).toLowerCase().slice(1);
    return filters.some(
      (filter) => filter.extensions.includes("*") || filter.extensions.includes(ext)
    );
  }
  /**
   * 递归获取指定目录下的所有文件
   */
  async getFilesRecursively(dirPath, filters) {
    const files = [];
    const that = this;
    async function traverse(currentPath) {
      const entries = await fs__namespace.promises.readdir(currentPath, { withFileTypes: true });
      for (const entry of entries) {
        const fullPath = path__namespace.join(currentPath, entry.name);
        if (entry.isDirectory()) {
          await traverse(fullPath);
        } else if (entry.isFile()) {
          if (!filters || that.isFileMatchFilter(entry.name, filters)) {
            files.push(fullPath);
          }
        }
      }
    }
    await traverse(dirPath);
    return files;
  }
  async fileUpload(source, result) {
    if (!result.canceled && result.filePaths.length > 0) {
      console.log("拖拽上传文件---", result.filePaths, source);
      for (const filePath of result.filePaths) {
        const worker = this.createChatWorker(filePath);
        const fileId = `${source}_${path__namespace.basename(filePath)}_${Date.now()}`;
        worker.postMessage({
          type: "upload",
          source,
          fileId,
          filePath,
          token: this.token,
          uploadApiUrl: this.uploadApiUrl
        });
      }
    }
  }
  async chooseFiles(source, userIdForUpload, resourceId, resourceType, filters, remainingSlots) {
    const title = "选择要上传的文件";
    try {
      const now = Date.now();
      if (now - lastFileSelectTime < 3e3) {
        return { canceled: true, filePaths: [] };
      }
      lastFileSelectTime = now;
      const result = await electron.dialog.showOpenDialog(global.mainWindow, {
        properties: ["openFile", "multiSelections"],
        filters: filters || FileSyncManager.DEFAULT_FILTERS,
        title
      });
      if (!result.canceled && result.filePaths.length > 0) {
        const maxAllowed = remainingSlots || FileSyncManager.MAX_FILE_COUNT;
        if (result.filePaths.length > maxAllowed) {
          await electron.dialog.showMessageBox({
            type: "warning",
            title: "警告",
            message: "文件超过10个，请重新选择"
            // message: `You can add at most ${maxAllowed} files.`
          });
          return { canceled: true, filePaths: [], error: "FILE_LIMIT_EXCEEDED" };
        }
        for (const filePath of result.filePaths) {
          const stats = await fs__namespace.promises.stat(filePath);
          if (!this.isFileSizeValid(filePath, stats.size)) {
            return { canceled: true, filePaths: [], error: "FILE_SIZE_EXCEEDED" };
          }
        }
        console.log("上传文件---", result.filePaths, source);
        for (const filePath of result.filePaths) {
          const worker = this.createChatWorker(filePath);
          const fileId = `${source}_${path__namespace.basename(filePath)}_${Date.now()}`;
          worker.postMessage({
            type: "upload",
            source,
            fileId,
            filePath,
            token: this.token,
            userIdForUpload,
            uploadApiUrl: this.uploadApiUrl,
            resourceId,
            resourceType
          });
        }
        return { canceled: false, filePaths: result.filePaths, success: true };
      }
      return result;
    } catch (error) {
      throw error;
    }
  }
  /**
   * 检查文件大小是否超出限制
   */
  isFileSizeValid(filePath, size) {
    const ext = path__namespace.extname(filePath).toLowerCase().slice(1);
    const isImage = FileSyncManager.IMAGE_EXTENSIONS.includes(ext);
    const maxSize = isImage ? FileSyncManager.MAX_IMAGE_SIZE : FileSyncManager.MAX_OTHER_SIZE;
    if (size > maxSize) {
      const sizeInMB = maxSize / (1024 * 1024);
      electron.dialog.showMessageBox({
        type: "error",
        title: "Error",
        message: `File ${path__namespace.basename(filePath)} exceeds ${isImage ? "image" : "file"} size limit（${sizeInMB}MB）`
      });
      return false;
    }
    return true;
  }
  /**
   * 更新上传API地址
   * @param uploadApiUrl
   * @returns
   */
  updateUploadApiUrl(uploadApiUrl) {
    this.uploadApiUrl = uploadApiUrl;
    console.log("更新上传API地址:", this.uploadApiUrl);
  }
  /**
   * 更新上传API信息
   * @param apiToken 上传令牌
   * @param uploadApiUrl 上传API地址
   */
  updateUploadApiInfo({
    apiToken,
    uploadApiUrl
  }) {
    this.token = apiToken;
    this.uploadApiUrl = uploadApiUrl;
    console.log("更新上传API信息:", { apiToken, uploadApiUrl });
  }
  /**
   * 选择并处理文件
   * @param source 文件来源，PKB或TKB的knowledgeId
   * @param filters 可选，文件过滤器，默认为标准文档和图片格式
   * @param dialogTitle 可选，对话框标题，默认根据source生成
   * @returns 对话框结果，包含选中的文件路径和来源信息
   */
  async selectFiles({
    source,
    filters,
    dialogTitle,
    isLimitFileCount = true,
    userIdForUpload,
    resourceId,
    resourceType
  }) {
    const actualFilters = filters || FileSyncManager.DEFAULT_FILTERS;
    let title = dialogTitle || "选择要上传的文件";
    const result = await electron.dialog.showOpenDialog(global.mainWindow, {
      properties: ["openFile", "multiSelections"],
      filters: actualFilters,
      title
    });
    if (!result.canceled && result.filePaths.length > 0) {
      if (isLimitFileCount && result.filePaths.length > FileSyncManager.MAX_FILE_COUNT) {
        electron.dialog.showMessageBox({
          type: "error",
          title: "Error",
          message: `Maximum ${FileSyncManager.MAX_FILE_COUNT} files can be selected`
        });
        return;
      }
      for (const filePath of result.filePaths) {
        const stats = await fs__namespace.promises.stat(filePath);
        if (!this.isFileSizeValid(filePath, stats.size)) {
          return;
        }
      }
      await this.processSelectedFiles(result.filePaths, source, resourceId, resourceType);
    }
    if ([SyncStatus.COMPLETED, SyncStatus.IDLE].includes(this.status) && this.pendingFiles.length > 0) {
      this.userIdForUpload = userIdForUpload;
      this.autoUpload();
    }
  }
  /**
   * 处理选择的文件
   */
  async processSelectedFiles(filePaths, source, resourceId, resourceType) {
    const fileInfos = [];
    for (const filePath of filePaths) {
      try {
        const stats = await fs__namespace.promises.stat(filePath);
        const parsedPath = path__namespace.parse(filePath);
        const fileInfo = {
          path: filePath,
          name: parsedPath.base,
          size: stats.size,
          lastModified: stats.mtimeMs,
          isDirectory: stats.isDirectory(),
          source,
          fileId: `${source}_${parsedPath.base}_${Date.now()}`,
          // 使用时间戳构建唯一文件ID
          status: FileStatus.PENDING,
          resourceId,
          resourceType
        };
        fileInfos.push(fileInfo);
      } catch (error) {
        console.error(`处理文件错误 ${filePath}:`, error);
      }
    }
    this.pendingFiles = [...this.pendingFiles, ...fileInfos];
    this.fileStateStore.saveStates(this.pendingFiles);
    this.emitEvent(SyncEvents.FILE_LIST_UPDATED, {
      source,
      files: this.getFilesBySource(source),
      pendingFiles: this.pendingFiles
    });
  }
  createChatWorker(filePath) {
    const worker = new worker_threads.Worker(
      getResourcesPath(path__namespace.join(__dirname, "../../resources/worker/fileUpload.js"))
    );
    worker.on("message", (event) => {
      const { type, fileId, data, error, source } = event;
      if (type === "success") {
        const filename = getFilenameFromPath(filePath);
        this.emitEvent(SyncEvents.CHAT_UPLOAD_COMPLETED, {
          fileId,
          source,
          data,
          filename,
          path: filePath
        });
      } else if (type === "error") {
        this.emitEvent(SyncEvents.SYNC_FAILED, { fileId, source, error });
      }
    });
    return worker;
  }
  /**
   * 创建Worker
   */
  createWorker() {
    const worker = new worker_threads.Worker(
      getResourcesPath(path__namespace.join(__dirname, "../../resources/worker/fileUpload.js"))
    );
    worker.on("message", (event) => {
      const { type, fileId, data, error, source, resourceId, resourceType } = event;
      console.log("Worker message received:", event);
      if (type === "open") {
        this.changeFilesStatus([fileId], FileStatus.UPLOADING);
      } else if (type === "progress") {
        this.emitEvent(SyncEvents.FILE_PROGRESS_UPDATE, {
          fileId,
          progress: data.progress,
          uploaded: data.uploaded,
          total: data.total
        });
      } else if (type === "success") {
        console.log("上传成功", fileId, data);
        const fileIndex = this.pendingFiles.findIndex((f) => f.fileId === fileId);
        if (fileIndex !== -1) {
          this.progress.processed++;
          this.pendingFiles.splice(fileIndex, 1);
          this.fileStateStore.saveStates(this.pendingFiles);
          this.emitEvent(SyncEvents.ONE_UPLOAD_COMPLETED, {
            source,
            sourceFiles: this.getFilesBySource(source),
            pendingFiles: this.pendingFiles,
            resourceId,
            resourceType
          });
        }
        this.uploadNext();
      } else if (type === "error") {
        const fileIndex = this.pendingFiles.findIndex((f) => f.fileId === fileId);
        if (fileIndex !== -1) {
          this.progress.processed++;
          this.progress.conflicts++;
          this.changeFilesStatus([fileId], FileStatus.FAILED);
        }
        this.uploadNext();
      } else if (type === "pause") {
        this.changeFilesStatus([fileId], FileStatus.PAUSED);
        this.uploadNext();
      } else if (type === "delete") {
        this.deleteFiles([fileId]);
        this.uploadNext();
      }
    });
    return worker;
  }
  /**
   * 上传下一个文件
   */
  uploadNext() {
    if (this.currentWorker) {
      this.currentWorker.terminate();
      this.currentWorker = void 0;
      this.currentFileId = void 0;
    }
    const nextFile = this.pendingFiles.find((f) => f.status === FileStatus.PENDING);
    if (!nextFile) {
      this.status = SyncStatus.COMPLETED;
      console.log("所有文件上传完成");
      return;
    }
    this.currentWorker = this.createWorker();
    this.currentFileId = nextFile.fileId;
    console.log("this uploadApiUrl", this.uploadApiUrl);
    this.emitEvent(SyncEvents.START_UPLOAD_TO_WORKER, {
      isCurrentWorker: !!this.currentWorker,
      postMessageData: {
        type: "postMessage upload to worker",
        source: nextFile.source,
        fileId: nextFile.fileId,
        filePath: nextFile.path,
        token: this.token,
        userIdForUpload: this.userIdForUpload,
        uploadApiUrl: this.uploadApiUrl,
        resourceId: nextFile.resourceId,
        resourceType: nextFile.resourceType
      }
    });
    this.currentWorker.postMessage({
      type: "upload",
      source: nextFile.source,
      fileId: nextFile.fileId,
      filePath: nextFile.path,
      token: this.token,
      userIdForUpload: this.userIdForUpload,
      uploadApiUrl: this.uploadApiUrl,
      resourceId: nextFile.resourceId,
      resourceType: nextFile.resourceType
    });
  }
  async autoUpload() {
    try {
      if (this.pendingFiles.length === 0) {
        return { success: false, message: "没有待上传的文件" };
      }
      if (this.isRendererReady) {
        return this.executeAutoUpload();
      }
      this.pendingAutoUpload = true;
      this.startRendererReadyCheck();
      return { success: true, message: "等待渲染进程就绪后开始上传" };
    } catch (error) {
      this.status = SyncStatus.FAILED;
      this.emitEvent(SyncEvents.SYNC_FAILED, { error });
      return {
        success: false,
        message: error instanceof Error ? error.message : "上传过程中发生未知错误"
      };
    }
  }
  /**
   * 执行上传流程
   */
  async executeAutoUpload() {
    try {
      if (this.pendingFiles.length === 0) {
        return { success: false, message: "没有待上传的文件" };
      }
      this.emitEvent(SyncEvents.START_UPLOAD_TO_WORKER, { isReady: true });
      this.status = SyncStatus.SYNCING;
      this.resetProgress();
      this.progress.total = this.pendingFiles.length;
      this.uploadNext();
      return { success: true };
    } catch (error) {
      this.status = SyncStatus.FAILED;
      this.emitEvent(SyncEvents.SYNC_FAILED, { error });
      return {
        success: false,
        message: error instanceof Error ? error.message : "上传过程中发生未知错误"
      };
    }
  }
  /**
   * 开始检查渲染进程就绪状态
   */
  startRendererReadyCheck() {
    if (this.rendererReadyCheckInterval) {
      return;
    }
    this.rendererReadyCheckInterval = setInterval(() => {
      if (this.isRendererReady) {
        if (this.rendererReadyCheckInterval) {
          clearInterval(this.rendererReadyCheckInterval);
          this.rendererReadyCheckInterval = void 0;
        }
        return;
      }
    }, 500);
  }
  /**
   * 开始上传暂停和错误的文件
   */
  startUpload(fileIds) {
    this.changeFilesStatus(fileIds, FileStatus.PENDING);
    if ([SyncStatus.COMPLETED, SyncStatus.IDLE].includes(this.status) && this.pendingFiles.length > 0) {
      this.autoUpload();
    }
    return { success: true, message: "已开启上传" };
  }
  // 暂停上传
  puauseUpload(fileIds) {
    this.changeFilesStatus(fileIds, FileStatus.PAUSED);
    return { success: true, message: "已暂停上传" };
  }
  // 暂停上传中任务
  puauseUploadingTask(fileIds) {
    if (this.currentWorker) {
      for (const fileId of fileIds) {
        this.currentWorker.postMessage({
          type: "pause",
          fileId
        });
      }
    }
    return { success: true, message: "已暂停上传" };
  }
  /**
   * 更改文件状态
   * @param fileIds 要更改状态的文件ID数组
   * @param status
   */
  changeFilesStatus(fileIds, status) {
    let source;
    for (const fileId of fileIds) {
      const fileIndex = this.pendingFiles.findIndex((f) => f.fileId === fileId);
      if (fileIndex !== -1) {
        if (this.pendingFiles[fileIndex].status !== status) {
          this.pendingFiles[fileIndex].status = status;
        }
        source = this.pendingFiles[fileIndex].source;
      }
    }
    if (source) {
      this.emitEvent(SyncEvents.FILE_LIST_UPDATED, {
        source,
        files: this.getFilesBySource(source),
        pendingFiles: this.pendingFiles
      });
      this.fileStateStore.saveStates(this.pendingFiles);
    }
  }
  /**
   * 删除上传中的任务
   * @param fileIds 要删除的文件ID数组
   */
  deleteUploadingTask(fileIds) {
    let source;
    let delFiles = [];
    if (this.currentWorker) {
      for (const fileId of fileIds) {
        this.currentWorker.postMessage({
          type: "delete",
          fileId
        });
      }
    }
    for (const fileId of fileIds) {
      const fileIndex = this.pendingFiles.findIndex((f) => f.fileId === fileId);
      if (fileIndex !== -1) {
        delFiles = this.pendingFiles.splice(fileIndex, 1);
        this.progress.deleted += 1;
        this.progress.total -= 1;
      }
    }
    if (delFiles.length > 0) {
      source = delFiles[0].source;
      this.emitEvent(SyncEvents.FILE_LIST_UPDATED, {
        source,
        files: this.getFilesBySource(source),
        pendingFiles: this.pendingFiles
      });
      this.fileStateStore.saveStates(this.pendingFiles);
    }
    return { success: true, message: "已删除上传中的任务" };
  }
  /**
   * 删除指定文件ID列表中的文件
   * @param fileIds 要删除的文件ID数组
   */
  deleteFiles(fileIds) {
    let source;
    let delFiles = [];
    for (const fileId of fileIds) {
      const fileIndex = this.pendingFiles.findIndex((f) => f.fileId === fileId);
      if (fileIndex !== -1) {
        delFiles = this.pendingFiles.splice(fileIndex, 1);
        this.progress.deleted += 1;
        this.progress.total -= 1;
      }
    }
    if (delFiles.length > 0) {
      source = delFiles[0].source;
      this.emitEvent(SyncEvents.FILE_LIST_UPDATED, {
        source,
        files: this.getFilesBySource(source),
        pendingFiles: this.pendingFiles
      });
      this.fileStateStore.saveStates(this.pendingFiles);
    }
  }
  /**
   * 获取上传令牌（模拟实现）
   */
  async getUploadToken() {
    await new Promise((resolve) => setTimeout(resolve, 500));
    return {
      token: `upload_token_${Date.now()}`,
      expires: Date.now() + 36e5
      // 1小时后过期
    };
  }
  /**
   * 重置进度信息
   */
  resetProgress() {
    this.progress = {
      total: 0,
      processed: 0,
      deleted: 0,
      conflicts: 0
    };
  }
  /**
   * 发送事件到渲染进程
   */
  emitEvent(event, data) {
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      try {
        this.mainWindow.webContents.send(event, data);
        console.log(`webContents event ${event} sent successfully`, data);
      } catch (error) {
        console.error(`Failed to emit event ${event}:`, error);
      }
    } else {
      console.warn(`Cannot emit ${event}: mainWindow is not available`);
    }
  }
  /**
   * 获取当前同步状态
   */
  getStatus() {
    return this.status;
  }
  /**
   * 获取当前同步进度
   */
  getProgress() {
    return this.progress;
  }
  /**
   * 获取待同步文件列表
   */
  getPendingFiles() {
    return this.pendingFiles;
  }
  /**
   * 清除待同步文件列表
   */
  clearFiles(source) {
    this.pendingFiles = this.pendingFiles.filter((f) => f.source !== source);
    this.emitEvent(SyncEvents.FILE_LIST_UPDATED, {
      source,
      files: [],
      pendingFiles: this.pendingFiles
    });
  }
  /**
   * 获取指定来源的文件
   */
  getFilesBySource(source) {
    return this.pendingFiles.filter((f) => f.source === source);
  }
  /**
   * 清理资源
   */
  dispose() {
    if (this.currentWorker) {
      this.currentWorker.terminate();
      this.currentWorker = void 0;
    }
  }
  /**
   * 选择自定义路径文件上传
   * @param source 文件来源，PKB或TKB的knowledgeId
   * @returns
   */
  async selectCustomPathFilesUpload(folderPath, source, resourceId, resourceType) {
    const fileInfoArr = await this.getFilesByPath(folderPath, source, resourceId, resourceType);
    if (fileInfoArr.length > 0) {
      await this.processSelectedFiles(
        fileInfoArr.map((fileInfoItem) => fileInfoItem.path),
        source,
        // PrivateKBSource, // 'PKB'
        resourceId,
        resourceType
      );
    }
    if ([SyncStatus.COMPLETED, SyncStatus.IDLE].includes(this.status) && this.pendingFiles.length > 0) {
      this.autoUpload();
    }
  }
  /**
   * 自动上传已有待上传文件
   * @param
   * @return
   */
  async autoUploadPendingFiles() {
    if ([SyncStatus.COMPLETED, SyncStatus.IDLE].includes(this.status) && this.pendingFiles.length > 0) {
      this.autoUpload();
    } else {
      console.log("没有待上传的文件");
    }
  }
  /**
   * 清理资源
   */
  destroy() {
    if (this.rendererReadyCheckInterval) {
      clearInterval(this.rendererReadyCheckInterval);
      this.rendererReadyCheckInterval = void 0;
    }
    if (this.currentWorker) {
      this.currentWorker.terminate();
      this.currentWorker = void 0;
    }
    if (this.currentChatWorker) {
      this.currentChatWorker.terminate();
      this.currentChatWorker = void 0;
    }
  }
}
logCombine(electron.app.getPath("logs"));
const mainWinInit = () => {
  const win = createMainWin();
  electron.app.on("second-instance", (_, argv) => {
    console.info("second-instance", "second instance detected.");
    toLoginWithCode(win, argv);
    win.isVisible() || handleWinShow(win);
  });
  IpcRegister(win);
  new FileSyncManager(win);
  return win;
};
electron.app.whenReady().then(async () => {
  console.info("main mode=>", "development");
  console.info("env", void 0);
  await Config.init();
  electron.session.defaultSession.setPermissionRequestHandler((webContents, permission, callback, details) => {
    try {
      if (permission === "media") {
        const mediaTypes = details?.mediaTypes || [];
        const needsMic = mediaTypes.length === 0 || mediaTypes.includes("audio");
        const origin = details?.securityOrigin || details?.requestingUrl || "";
        const allowedOrigins = [
          "http://teamai-test.lenovo.com",
          "http://*************:9070",
          "http://*************:9080",
          "http://*************:8099",
          "http://*************:8102"
        ];
        const isAllowed = origin.startsWith("file:") || origin.startsWith("app:") || allowedOrigins.some((o) => origin.startsWith(o));
        callback(needsMic && isAllowed);
        return;
      }
      callback(false);
    } catch (e) {
      console.error("Permission handler error:", e);
      callback(false);
    }
  });
  if (electron.app.isPackaged && process.argv.some((arg) => arg.startsWith("ainow://"))) {
    try {
      await electron.session.defaultSession.clearStorageData({ storages: ["localstorage"] });
    } catch (e) {
    }
  }
  setProtocolScheme();
  const gotTheLock = electron.app.requestSingleInstanceLock();
  if (!gotTheLock) {
    console.info("Another instance is running.");
    electron.app.quit();
  }
  utils.electronApp.setAppUserModelId("com.electron");
  electron.protocol.handle("app", async (request) => {
    const url = new URL(request.url);
    const filePath = path.join(__dirname, "../" + url.hostname, url.pathname);
    try {
      const data = await promises.readFile(filePath);
      let mimeType = "text/html";
      if (filePath.endsWith(".js")) mimeType = "text/javascript";
      else if (filePath.endsWith(".css")) mimeType = "text/css";
      else if (filePath.endsWith(".png")) mimeType = "image/png";
      return new Response(new Uint8Array(data), {
        headers: { "Content-Type": mimeType }
      });
    } catch (error) {
      return new Response("Not Found", { status: 404 });
    }
  });
  electron.app.on("browser-window-created", (_, window) => {
    utils.optimizer.watchWindowShortcuts(window);
  });
  mainWinInit();
  electron.app.on("window-all-closed", () => {
    console.log("window-all-closed");
    if (process.platform !== "darwin") {
      electron.app.quit();
    }
  });
});
