import {
  getNonceToken,
  exchangeToken,
  refreshToken,
  getLoginUrl,
  exchangeCode,
  verifyUser
} from '../../../services'
// import { getCookie, setCookie } from "@/utils";
import { electronHooks } from '../electron'
import { GlobalConfig, LocalStore } from '../common'
import { error, info } from 'console'
import { LOGIN_STATUS } from '@/types'
import { Ref } from 'vue'
import { AMsg } from '@libs/a-comps'

export function loginHooks(curStatus: Ref<LOGIN_STATUS>, afterLogin: (res: LOGIN_STATUS) => void) {
  const electronApi = electronHooks()
  const clientId = '043af4c56d82afaa21e0cc34b64cea38de110e4effb64b9db480423b032a2ac6'
  const client_key = '2abf836b15427c5c78eb6d3f'
  const ainowRealm = 'neorow.smbsolutioncenter.lenovo.com'
  const device_info = {
    device_id: '123456789',
    product_id: 'team-ai'
  }

  const handleCodeToToken = async (code: string) => {
    try {
      console.info('LID code', code)
      const token = (await codeToToken(code)).data.access_token

      console.info('LID token', token)
      const authTokens = await getAuthToken(token)
      console.info('auth', token)
      if (authTokens) {
        return authTokens
      }
      console.info('authTokens', GlobalConfig.tokens)
      return false
    } catch (error) {
      console.error(error)
      return false
    }
  }
  const getAuthToken = async (access_token: string, realm: string = ainowRealm) => {
    try {
      const nonceTokenRes = await getNonceToken()
      if (nonceTokenRes.data.code === 0) {
        const authTokenRes = await exchangeToken(nonceTokenRes.data.data as string, {
          client_key,
          device_info,
          lenovo_id_info: {
            access_token,
            realm
          }
        })
        return authTokenRes.data.data
      }
    } catch (error) {
      console.log(error)
    }
  }
  const refreshAuthToken = async (refresh_token: string) => {
    try {
      const nonceTokenRes = await getNonceToken()
      if (nonceTokenRes.data.code === 0) {
        const refreshTokenRes = await refreshToken(nonceTokenRes.data.data as string, {
          client_key,
          refresh_token
        })
        return refreshTokenRes.data.data
      }
    } catch (error) {
      console.log(error)
      return
    }
  }
  const getTokensFromStore = () => {
    try {
      // const
      const res = JSON.parse(LocalStore.getVal('tokens') || '')
      return res
    } catch (error) {
      return ''
    }
  }
  const setTokenToStore = () => {
    LocalStore.setVal('tokens', JSON.stringify(GlobalConfig.tokens))

    // 更新主进程token
    window.api.updateUploadApiInfo({
      apiToken: GlobalConfig.tokens.access_token,
      uploadApiUrl: `${GlobalConfig.kbFileServer}/api/v1/document/upload`
    })
  }

  const toLenovoId = () => {
    //     const code = 'test'
    //     console.log(getLoginUrl(clientId, code));
    //     // return
    //     location.href = getLoginUrl(clientId, code)//TODO:处理跳转地址
  }
  const codeToToken = (code: string) => {
    return exchangeCode({
      grant_type: 'authorization_code',
      redirect_uri: (window as any).electron ? 'ainow.row://' : 'http://localhost:5173', // todo web网址链接
      code,
      client_id: clientId,
      scope: 'openid',
      code_verifier: 'test'
    })
  }

  const toLogin = async () => {
    // electron
    if (electronApi) {
      electronApi.openWebSite(getLoginUrl(clientId))
    } else {
      // web
      window.open(getLoginUrl(clientId), '_blank')
    }
  }
  const init = async () => {
    try {
      Object.assign(GlobalConfig.tokens, getTokensFromStore())
      if (GlobalConfig.tokens.access_token) {
        const tokenRes = await refreshAuthToken(GlobalConfig.tokens.refresh_token)
        if (tokenRes) {
          Object.assign(GlobalConfig.tokens, tokenRes)
          console.info(GlobalConfig)
          setTokenToStore()
          const userRes = toVerifyUser()
          setInterval(
            async () => {
              const tokenRes = await refreshAuthToken(GlobalConfig.tokens.refresh_token)
              Object.assign(GlobalConfig.tokens, tokenRes)
              setTokenToStore()
            },
            25 * 60 * 1000
          )
          return userRes
        }
      }
      return LOGIN_STATUS.NO_LOGIN
    } catch (error) {
      console.error(error)

      return LOGIN_STATUS.NO_LOGIN
    }
  }
  const toVerifyUser = async () => {
    try {
      const res = await verifyUser()

      if (res.status === 200) {
        if (res.data.code === 'A1001') {
          return LOGIN_STATUS.NO_AUTH
        }

        return LOGIN_STATUS.LOGIN
      }
      return LOGIN_STATUS.NO_LOGIN
    } catch (error) {
      console.error('verify接口异常:', error)
      AMsg.error({
        content: () => '请重试',
        class: 'login-a-msg',
        duration: 0,
        style: {
          marginTop: '60vh'
        }
      })
      return LOGIN_STATUS.NO_LOGIN
    }
  }
  const loginWithCodeCallback = (code: string) => {
    if (curStatus.value !== LOGIN_STATUS.LOGIN_LOADING) {
      console.warn('not time to get code')
      return
    }

    handleCodeToToken(code).then((res) => {
      if (res) {
        Object.assign(GlobalConfig.tokens, res)
        setTokenToStore()
        toVerifyUser()
          .then((res) => {
            afterLogin(res)
          })
          .catch((err) => {
            console.error(err)
          })
      }
    })
  }
  electronApi?.toLoginWithCode((e, { code }) => {
    loginWithCodeCallback(code)
  })
  return {
    toLogin,
    loginWithCodeCallback,
    init,
    codeToToken,
    getAuthToken,
    refreshAuthToken
  }
}
export const handleSignout = () => {
  GlobalConfig.tokens.access_token = ''
  GlobalConfig.tokens.refresh_token = ''
  GlobalConfig.tokens.lenovo_id = ''
  GlobalConfig.tokens.lenovo_username = ''

  try {
    localStorage.removeItem('tokens')
  } catch (e) {}
}
