<template>
  <div class="toolbar-box" v-if="isAnswerComplete">
    <div class="toolbar-box_turning" v-if="shouldShowTurning">
      <SvgIcon
        name="LeftArrow"
        :size="'20'"
        :color="refreshIndex <= 1 ? '#9C9C9C' : '#3B3B3B'"
        @click="flipLeft"
      />
      <div class="toolbar-box_turning_pages">
        {{ pageInfo.currentPage }} / {{ pageInfo.totalPages }}
      </div>
      <SvgIcon
        name="RightArrow"
        :size="'20'"
        :color="refreshIndex >= pageInfo.totalPages ? '#9C9C9C' : '#3B3B3B'"
        @click="flipRight"
      />
    </div>
    <div class="toolbar-box_functionality">
      <SvgIcon name="copy" :size="'20'" v-if="!isCopyDone" color="#3B3B3B" @click="clickCopy" />
      <SvgIcon name="CopyDone" :size="'20'" v-else color="#3B3B3B" />
      <!-- <SvgIcon name="share" :size="'20'" color="#3B3B3B" @click="exportPDF" /> -->
      <SvgIcon name="refresh" :size="'20'" color="#3B3B3B" @click="clickRefresh" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ChatController, ChatStatus } from '@libs/a-comps'
import { Answer } from '@libs/a-comps/ChatBaseComponent/types/ChatClass'
import SvgIcon from '@renderer/components/SvgIcon/SvgIcon.vue'
import { computed, ref, onMounted, onUnmounted } from 'vue'
import { emitter } from '@/utils/EventBus'

const props = defineProps<{
  chat: ChatController | undefined
  answerItem: Answer | undefined
}>()

const isCopyDone = ref(false)

const isAnswerComplete = computed(() => {
  if (!props.answerItem) return false

  return [ChatStatus.DONE, ChatStatus.SUCCESS].includes(props.answerItem.chatStatusAnswer.value)
})

// 计算当前页码和总页数
const pageInfo = computed(() => {
  if (!props.answerItem) {
    return { currentPage: 0, totalPages: 0, showTurning: false }
  }

  // 如果是从历史会话加载的
  if (props.answerItem.isFromHistory) {
    // 对于历史会话 refreshArr 存储的是所有历史版本（不包括当前版本）
    // 总页数应该是 refreshArr.length + 1（当前版本）
    const totalPages = props.answerItem.refreshArr.length + 1
    return {
      currentPage: refreshIndex.value,
      totalPages,
      showTurning: totalPages > 1
    }
  } else {
    // 正常问答流程
    const totalPages = props.answerItem.refreshArr.length + 1
    return {
      currentPage: refreshIndex.value,
      totalPages,
      showTurning: totalPages > 1
    }
  }
})

// 是否显示翻页按钮
const shouldShowTurning = computed(() => {
  if (!props.answerItem) return false

  // 计算总页数（当前内容 + 历史版本）
  const totalPages = props.answerItem.refreshArr.length + 1

  // 只在初始化时设置页码（refreshIndex为 0 时）
  if (refreshIndex.value === 0) {
    if (props.answerItem.isFromHistory) {
      // 历史会话：显示第一页（最新内容）
      refreshIndex.value = 1
    } else {
      // 正常问答：显示最后一页（最新内容）
      refreshIndex.value = totalPages
    }
  }

  // 确保页码在有效范围内
  if (refreshIndex.value < 1) {
    refreshIndex.value = 1
  }
  if (refreshIndex.value > totalPages) {
    refreshIndex.value = totalPages
  }

  return totalPages > 1
})

const refreshIndex = ref(0)

const flipLeft = () => {
  if (!props.answerItem) return
  if (refreshIndex.value <= 1) {
    return
  }
  refreshIndex.value--
  updateContentDisplay()
}

const sendCurPageIndex = () => {
  emitter.emit('history-page-index', refreshIndex.value)
}

const flipRight = () => {
  if (!props.answerItem) return
  const totalPages = pageInfo.value.totalPages
  if (refreshIndex.value >= totalPages) {
    return
  }
  refreshIndex.value++

  updateContentDisplay()
}

// 更新内容显示
const updateContentDisplay = () => {
  if (!props.answerItem) return

  if (props.answerItem.isFromHistory) {
    // 历史会话逻辑
    if (refreshIndex.value === 1) {
      // 第一页显示最新内容（历史记录中的最新版本）
      props.answerItem.setRefreshContent(-1)
    } else {
      // 其他页显示历史版本内容
      const contentIndex = refreshIndex.value - 2
      props.answerItem.setRefreshContent(contentIndex)
    }
  } else {
    // 正常问答逻辑：页码顺序与内容版本顺序一致
    if (refreshIndex.value === pageInfo.value.totalPages) {
      // 最后一页显示最新生成的内容，如果之前保存了临时内容，则恢复它
      if (props.answerItem.tempContent && props.answerItem.tempResponse) {
        props.answerItem.answerData.content = props.answerItem.tempContent
        props.answerItem.answerData.response = props.answerItem.tempResponse
        // 恢复thinkingContent
        if (props.answerItem.tempThinkingContent) {
          props.answerItem.thinkingContent = props.answerItem.tempThinkingContent
        }
        // 恢复最新页的深度思考标记（避免从历史页切回后仍保留旧的标记导致标题错误）
        if (typeof props.chat?.isDeeoThinking === 'boolean') {
          props.answerItem.isDeepThink = props.chat.isDeeoThinking
        }
        // 清除临时保存的内容
        props.answerItem.tempContent = undefined
        props.answerItem.tempResponse = undefined
        props.answerItem.tempThinkingContent = undefined
      }
    } else {
      // 其他页显示历史版本内容，在切换到历史版本之前，保存当前内容
      if (!props.answerItem.tempContent) {
        props.answerItem.tempContent = props.answerItem.answerData.content
        props.answerItem.tempResponse = props.answerItem.answerData.response
        props.answerItem.tempThinkingContent = props.answerItem.thinkingContent
      }

      // refreshIndex=1 显示最早的历史版本，refreshIndex=2 显示较新的历史版本
      const contentIndex = refreshIndex.value - 1
      if (contentIndex >= 0 && contentIndex < props.answerItem.refreshArr.length) {
        props.answerItem.setRefreshContent(contentIndex)
      }
    }
  }

  // 最后发送当前页码，确保标题根据最新内容计算
  sendCurPageIndex()
}

const clickRefresh = () => {
  props.chat?.setRegenerate(true)

  if (props.answerItem?.answerData && props.chat?.getChatStatus() === ChatStatus.DONE) {
    const currentAnswer = props.answerItem
    if (currentAnswer?.fileSearchList && currentAnswer.fileSearchList.length > 0) {
      const documentIds = currentAnswer.fileSearchList.map((file) => file.documentId)
      props.chat?.setDocumentIds(documentIds)
    }

    // 保存当前答案到refreshArr（只有在非历史会话时才需要）
    if (currentAnswer && !currentAnswer.isFromHistory) {
      // 克隆当前答案（这是即将被替换的答案）
      const originalAnswer = currentAnswer.clone()

      const independentContent = {
        content: currentAnswer.answerData.content,
        response: currentAnswer.answerData.response
      }

      // 确保克隆的对象有自己的内容副本，不引用后续会变化的内容
      originalAnswer.answerData.content = independentContent.content
      originalAnswer.answerData.response = independentContent.response
      originalAnswer.originalContent = independentContent.content
      originalAnswer.originalResponse = independentContent.response

      currentAnswer.setRefresh(originalAnswer)

      // 清除临时保存的内容
      currentAnswer.tempContent = undefined
      currentAnswer.tempResponse = undefined
      currentAnswer.tempThinkingContent = undefined
      // 标记为已添加，防止listenMessage中重复添加
      currentAnswer.hasAddedToRefresh = true
    }

    // 清空当前会话答案相关内容
    emitter.emit('clear-answer-content', props.answerItem.id)

    // 通知关闭ref&citicles panel
    emitter.emit('ref-click-2', false)
    emitter.emit('ref-click-expand', false)
    props.chat?.startRegenerate(props.answerItem?.answerData)
  }
}

/**
 * 点击复制按钮时的处理函数
 * 调用浏览器的剪贴板API将props.answerItem的answerData.response属性复制到剪贴板，
 * 如果该属性不存在，则复制"复制失败"字符串。
 *
 * @returns 无返回值
 */
const clickCopy = () => {
  console.log('copy', props.chat)
  navigator.clipboard
    .writeText(props.answerItem?.answerData.response || 'copy failed')
    .then(() => {
      console.log(props.answerItem, 'copy OK')
      isCopyDone.value = true
      setTimeout(() => {
        isCopyDone.value = false
      }, 2000)
    })
    .catch((err) => {
      console.error('Failed to copy text: ', err)
    })
}

const exportPDF = async () => {
  try {
    const pdfBuffer: Buffer = await window.api.printToPdf()

    const { filePath, canceled } = await window.api.showSaveDialog({
      title: '保存 PDF',
      defaultPath: `answer_${Date.now()}.pdf`,
      filters: [{ name: 'PDF文件', extensions: ['pdf'] }]
    })

    if (canceled || !filePath) return

    await window.api.savePdf({ buffer: pdfBuffer, path: filePath })
  } catch (err) {
    console.error('导出失败:', err)
  }
}

// 组件挂载时监听重置页码事件
let resetPageIndexListenerId: string | undefined
let chatCompletedListenerId: string | undefined

onMounted(() => {
  resetPageIndexListenerId = emitter.on('reset-page-index', () => {
    // 只对历史会话重置页码到第一页
    if (props.answerItem?.isFromHistory) {
      refreshIndex.value = 1
    }
  })

  // 监听聊天完成事件，在重新生成完成后更新页码
  chatCompletedListenerId = emitter.on('chat-completed', (data: any) => {
    // 只对正常问答流程且refreshArr不为空的情况更新页码（表明是重新生成）
    if (
      props.answerItem &&
      !props.answerItem.isFromHistory &&
      props.answerItem.refreshArr.length > 0
    ) {
      // 将页码设置为最新内容对应的页码（最后一页）
      const totalPages = props.answerItem.refreshArr.length + 1
      refreshIndex.value = totalPages
    }
  })
})

onUnmounted(() => {
  if (resetPageIndexListenerId) {
    emitter.removeListen('reset-page-index', resetPageIndexListenerId)
  }
  if (chatCompletedListenerId) {
    emitter.removeListen('chat-completed', chatCompletedListenerId)
  }
})
</script>

<style scoped lang="less">
.toolbar-box {
  // margin-left: 12px;
  display: flex;
  align-items: center;
  height: 20px;
  //   margin-top: 10px;

  &_turning {
    display: flex;
    align-items: center;
    justify-content: center;

    &_pages {
      font-weight: 400;
      font-size: 14px;
      line-height: 22px;
      color: var(--text-icon-gray-3-explain, rgba(82, 82, 91, 1));
    }

    :deep(.svgClass) {
      cursor: pointer;

      path {
        fill: currentColor !important;
      }
    }
  }

  &_functionality {
    display: flex;
    align-items: center;

    :deep(.svgClass) {
      margin-right: 8px;
      cursor: pointer;

      path {
        fill: currentColor !important;
      }
    }
  }
}
</style>
