<template>
  <div class="mic-test">
    <h2>麦克风权限测试页面</h2>
    
    <div class="test-section">
      <h3>1. 直接测试麦克风权限</h3>
      <button @click="testMicrophone" :disabled="isRecording">
        {{ isRecording ? '录音中...' : '测试麦克风' }}
      </button>
      <div v-if="micStatus" :class="['status', micStatus.type]">
        {{ micStatus.message }}
      </div>
    </div>

    <div class="test-section">
      <h3>2. iframe 内嵌测试</h3>
      <div class="iframe-container">
        <iframe
          src="data:text/html;charset=utf-8,%3Chtml%3E%3Chead%3E%3Ctitle%3EMic%20Test%3C/title%3E%3C/head%3E%3Cbody%3E%3Ch2%3EiFrame%20%E9%BA%A6%E5%85%8B%E9%A3%8E%E6%B5%8B%E8%AF%95%3C/h2%3E%3Cbutton%20onclick%3D%22testMic()%22%3E%E6%B5%8B%E8%AF%95%E9%BA%A6%E5%85%8B%E9%A3%8E%3C/button%3E%3Cdiv%20id%3D%22result%22%3E%3C/div%3E%3Cscript%3Efunction%20testMic()%7Bnavigator.mediaDevices.getUserMedia(%7Baudio%3Atrue%7D).then(stream%3D%3E%7Bdocument.getElementById('result').innerHTML%3D'%3Cspan%20style%3D%22color%3Agreen%22%3E%E9%BA%A6%E5%85%8B%E9%A3%8E%E6%9D%83%E9%99%90%E8%8E%B7%E5%8F%96%E6%88%90%E5%8A%9F%EF%BC%81%3C/span%3E'%3Bstream.getTracks().forEach(track%3D%3Etrack.stop())%7D).catch(err%3D%3E%7Bdocument.getElementById('result').innerHTML%3D'%3Cspan%20style%3D%22color%3Ared%22%3E%E9%BA%A6%E5%85%8B%E9%A3%8E%E6%9D%83%E9%99%90%E8%8E%B7%E5%8F%96%E5%A4%B1%E8%B4%A5%3A%20'%2Berr.message%2B'%3C/span%3E'%7D)%7D%3C/script%3E%3C/body%3E%3C/html%3E"
          allow="microphone; camera; autoplay"
          sandbox="allow-same-origin allow-scripts"
          width="100%"
          height="200"
          frameborder="0"
        ></iframe>
      </div>
    </div>

    <div class="test-section">
      <h3>3. 外部页面测试</h3>
      <div class="iframe-container">
        <iframe
          src="https://www.onlinemictest.com/"
          allow="microphone; camera; autoplay"
          sandbox="allow-same-origin allow-scripts allow-forms allow-popups"
          width="100%"
          height="400"
          frameborder="0"
        ></iframe>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const isRecording = ref(false)
const micStatus = ref<{ type: 'success' | 'error' | 'info', message: string } | null>(null)

const testMicrophone = async () => {
  isRecording.value = true
  micStatus.value = { type: 'info', message: '正在请求麦克风权限...' }
  
  try {
    const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
    micStatus.value = { type: 'success', message: '麦克风权限获取成功！可以正常录音。' }
    
    // 停止录音流
    stream.getTracks().forEach(track => track.stop())
    
    setTimeout(() => {
      isRecording.value = false
    }, 2000)
  } catch (error: any) {
    console.error('麦克风权限错误:', error)
    micStatus.value = { 
      type: 'error', 
      message: `麦克风权限获取失败: ${error.name} - ${error.message}` 
    }
    isRecording.value = false
  }
}
</script>

<style lang="less" scoped>
.mic-test {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  
  h3 {
    margin-top: 0;
    color: #333;
  }
  
  button {
    padding: 10px 20px;
    background: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    
    &:disabled {
      background: #6c757d;
      cursor: not-allowed;
    }
    
    &:hover:not(:disabled) {
      background: #0056b3;
    }
  }
}

.status {
  margin-top: 10px;
  padding: 10px;
  border-radius: 4px;
  
  &.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
  }
  
  &.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
  }
  
  &.info {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
  }
}

.iframe-container {
  margin-top: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  overflow: hidden;
}
</style>
